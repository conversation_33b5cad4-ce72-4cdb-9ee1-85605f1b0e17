<!DOCTYPE html>
<html lang="en">
<head>
    <!-- run with npx serve . -->
  <meta charset="UTF-8" />
  <title>Stripe + Supabase Test</title>

  <!-- 1) Load Stripe.js as a global -->
  <script src="https://js.stripe.com/v3/"></script>
  <!-- 2) Load Supabase client as an ESM module -->
  <script type="module">
    import { createClient } from 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js/+esm'

    // — your config (or read from env.js) —
    const SUPABASE_URL      = 'http://localhost:54321'
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
    const STRIPE_PK         = 'pk_test_51RhNxuD7PiFPw0TZp0aKECX9E3P2a6dMOfJNip0u3OPmGmjB1roT4NSWtdT3HamGZOimYpsTL2BLQzr2JDeVHO3000Y0ZfAuad'

    const STRIPE_PRICE_ID = 'price_1RhO2CD7PiFPw0TZiUeAgmVI'

    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
    let stripe, elements

    async function init() {
      // 1) Sign in (or getSession() if you’ve already signed in)
      const { data: { session }, error: signInError } =
        await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'pawordxyz123'
        })
      if (signInError) {
        console.error('Auth error', signInError)
        return
      }

      // 2) Create subscription via your local function
      const resp = await fetch(
        `${SUPABASE_URL}/functions/v1/create-subscription`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify({ priceId: STRIPE_PRICE_ID })
        }
      )
      const { clientSecret, error: fnError } = await resp.json()
      if (fnError) {
        console.error('Function error', fnError)
        return
      }

      // 3) Initialize Stripe *global* and mount the Payment Element
      stripe = Stripe(STRIPE_PK)
      elements = stripe.elements({ clientSecret })
      const paymentElement = elements.create('payment')
      paymentElement.mount('#payment-element')

      // 4) Wire up your submit button
      document
        .querySelector('button[type="submit"]')
        .addEventListener('click', handleSubmit)
    }

    async function handleSubmit(evt) {
      evt.preventDefault()
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {},
        redirect: 'if_required'
      })
      if (error) {
        alert(error.message)
      } else {
        alert('✅ Payment successful!')
      }
    }

    window.addEventListener('DOMContentLoaded', init)
  </script>
</head>
<body>
  <form id="payment-form">
    <div id="payment-element"></div>
    <button type="submit">Subscribe</button>
  </form>
</body>
</html>
