# Payment html example

 <!-- run with npx serve . -->

## env needed are public

```js
// supabase public anon key
const SUPABASE_ANON    = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'

// supabase public key
const STRIPE_PUBLISHABLE = '<your stripe pk_test_…>'
```




Other notes

[Stripe.js] Elements requires "width=device-width" be set in your page's viewport meta tag.
       For more information: https://stripe.com/docs/js/appendix/viewport_meta_requirements



apple pay may require domain?

controller-with-preconnect-0459f724e9afe5cd8a990d4c668f672b.js:1 [Stripe.js] You have not registered or verified the domain, so the following payment methods are not enabled in the Payment Element: 

- apple_pay

Please follow https://stripe.com/docs/payments/payment-methods/pmd-registration to register and verify the domain.