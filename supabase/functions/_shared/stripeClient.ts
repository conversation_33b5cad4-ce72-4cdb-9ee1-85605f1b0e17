import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import Stripe from 'https://esm.sh/stripe@14.21.0'

// Stripe client with secret key
export const stripe = new Stripe(Deno.env.get('SP_STRIPE_SECRET_KEY')!, {
  apiVersion: '2023-10-16',
  // Explicitly set httpClient for Deno compatibility
  httpClient: Stripe.createFetchHttpClient ? Stripe.createFetchHttpClient() : undefined
})

// Export Stripe type for use in other functions
export type { Stripe }