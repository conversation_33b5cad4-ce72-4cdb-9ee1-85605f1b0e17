# Supabase Edge Functions

This project follows Supabase's recommended "fat functions" pattern with shared code organization.

## Structure

```
supabase/functions/
├── import_map.json          # Top-level import map for all functions
├── _shared/                 # Shared code across functions
│   ├── supabaseAdmin.ts     # Supabase client with SERVICE_ROLE key
│   ├── stripeClient.ts      # Stripe client with secret key
│   ├── cors.ts              # Reusable CORS headers and response helpers
│   └── auth.ts              # Authentication utilities
├── create-subscription/     # Subscription creation function
│   ├── deno.json           # Function-specific Deno config
│   └── index.ts            # Main function code
├── list-subscriptions/      # Subscription listing function
│   ├── deno.json           # Function-specific Deno config
│   └── index.ts            # Main function code
├── stripe-webhook/          # Stripe webhook handler
│   ├── deno.json           # Function-specific Deno config
│   └── index.ts            # Main function code
└── tests/                   # Unit tests
    ├── create-subscription-test.ts
    └── list-subscriptions-test.ts
```

## Shared Modules

### `_shared/supabaseAdmin.ts`
Exports a Supabase client configured with the SERVICE_ROLE key for admin operations.

### `_shared/stripeClient.ts`
Exports a Stripe client configured with the secret key and the Stripe type for TypeScript.

### `_shared/cors.ts`
Provides reusable CORS headers and helper functions for creating consistent responses:
- `corsHeaders`: Standard CORS headers
- `createCorsResponse()`: Creates JSON response with CORS headers
- `createErrorResponse()`: Creates error response with CORS headers

### `_shared/auth.ts`
Authentication utilities:
- `authenticateUser()`: Validates JWT token and returns user data

## Benefits

1. **Reduced Duplication**: Common code like Supabase/Stripe client initialization is centralized
2. **Consistent Error Handling**: Standardized response formats across functions
3. **Better Maintainability**: Changes to shared logic only need to be made in one place
4. **Type Safety**: Shared TypeScript types ensure consistency
5. **Easier Testing**: Shared utilities can be tested independently

## Usage

Each function imports only what it needs from the shared modules:

```typescript
import { supabaseAdmin } from '../_shared/supabaseAdmin.ts'
import { stripe, type Stripe } from '../_shared/stripeClient.ts'
import { authenticateUser } from '../_shared/auth.ts'
import { createCorsResponse, createErrorResponse } from '../_shared/cors.ts'