import "jsr:@supabase/functions-js/edge-runtime.d.ts"

import { stripe, type Stripe } from '../_shared/stripeClient.ts'
import { supabaseAdmin } from '../_shared/supabaseAdmin.ts'
import { createErrorResponse } from '../_shared/cors.ts'

const endpointSecret = Deno.env.get('SP_STRIPE_WEBHOOK_SECRET')!

Deno.serve(async (req) => {
  console.log('=== Webhook Request Received ===')
  console.log('Method:', req.method)
  console.log('URL:', req.url)
  
  // Log all headers to debug
  const headers: Record<string, string> = {}
  req.headers.forEach((value, key) => {
    headers[key] = value
    if (key.toLowerCase().includes('stripe')) {
      console.log(`Found Stripe header: ${key} = ${value.substring(0, 50)}...`)
    }
  })
  console.log('All headers:', JSON.stringify(headers, null, 2))

  try {
    // Try different header name variations
    const sig = req.headers.get('stripe-signature') ||
                req.headers.get('Stripe-Signature') ||
                req.headers.get('STRIPE-SIGNATURE')
    
    if (!sig) {
      console.error('ERROR: Missing stripe signature header')
      console.error('Available headers:', Object.keys(headers))
      return createErrorResponse('Missing stripe signature', 400)
    }

    console.log('Found signature header, length:', sig.length)
    console.log('Webhook secret configured:', !!endpointSecret)
    console.log('Webhook secret length:', endpointSecret?.length)
    console.log('Webhook secret prefix:', endpointSecret?.substring(0, 10) + '...')

    const body = await req.text()
    console.log('Request body length:', body.length)
    console.log('Body preview:', body.substring(0, 100) + '...')
    
    let evt: Stripe.Event

    try {
      console.log('Attempting to verify webhook signature...')
      // Use constructEventAsync for Deno/Edge Runtime compatibility
      evt = await stripe.webhooks.constructEventAsync(body, sig, endpointSecret)
      console.log('SUCCESS: Signature verified!')
    } catch (error: any) {
      console.error('ERROR: Webhook signature verification failed')
      console.error('Error type:', error?.constructor?.name || 'Unknown')
      console.error('Error message:', error?.message || 'No message')
      console.error('Error details:', error)
      
      // Additional debugging for signature errors
      if (error?.message?.includes('signature')) {
        console.error('This is likely a signature mismatch issue')
        console.error('Check that SP_STRIPE_WEBHOOK_SECRET matches the webhook signing secret in Stripe Dashboard')
      }
      
      return createErrorResponse('Invalid signature', 400)
    }

    console.log(`Received webhook event: ${evt.type}`)

    // Handle checkout session completed (initial subscription creation)
    if (evt.type === 'checkout.session.completed') {
      const session = evt.data.object as Stripe.Checkout.Session
      
      // Get the user ID from client_reference_id or metadata
      const userId = session.client_reference_id || session.metadata?.user_id
      
      if (userId && session.subscription) {
        // Retrieve the subscription details
        const subscription = await stripe.subscriptions.retrieve(session.subscription as string)
        
        const { error } = await supabaseAdmin
          .from('user_subscriptions')
          .upsert({
            user_id: userId,
            subscription_id: subscription.id,
            status: subscription.status,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'subscription_id'
          })

        if (error) {
          console.error('Error creating subscription from checkout:', error)
          return createErrorResponse('Database error', 500)
        }
        
        console.log(`Created subscription ${subscription.id} for user ${userId}`)
      }
    }

    // Handle subscription events
    if (evt.type.startsWith('customer.subscription')) {
      const sub = evt.data.object as Stripe.Subscription
      
      // Lookup user by Stripe customer ID
      const { data: profile } = await supabaseAdmin
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', sub.customer)
        .single()

      if (profile) {
        const { error } = await supabaseAdmin
          .from('user_subscriptions')
          .upsert({
            user_id: profile.id,
            subscription_id: sub.id,
            status: sub.status,
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'subscription_id'
          })

        if (error) {
          console.error('Error updating subscription:', error)
          return createErrorResponse('Database error', 500)
        }
        
        console.log(`Updated subscription ${sub.id} status to ${sub.status}`)
      } else {
        console.warn(`No profile found for customer ${sub.customer}`)
      }
    }

    // Handle invoice events
    if (evt.type.startsWith('invoice.payment_')) {
      const invoice = evt.data.object as Stripe.Invoice
      console.log(`Invoice ${evt.type} for customer ${invoice.customer}`)
      
      if (invoice.subscription) {
        const status = evt.type === 'invoice.payment_succeeded' ? 'active' : 'past_due'
        
        await supabaseAdmin
          .from('user_subscriptions')
          .update({
            status,
            updated_at: new Date().toISOString()
          })
          .eq('subscription_id', invoice.subscription)
      }
    }

    return new Response(null, { status: 200 })

  } catch (error) {
    console.error('Webhook error:', error)
    return createErrorResponse('Internal server error', 500)
  }
})