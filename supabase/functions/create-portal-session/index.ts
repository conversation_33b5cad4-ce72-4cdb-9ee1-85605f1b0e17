import "jsr:@supabase/functions-js/edge-runtime.d.ts"

import { createCorsResponse, createErrorResponse, CORS_HEADERS } from "../_shared/cors.ts"
import { stripe } from "../_shared/stripeClient.ts"
import { authenticateUser } from "../_shared/auth.ts"
import { supabaseAdmin } from "../_shared/supabaseAdmin.ts"

Deno.serve(async (req) => {
  // CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: CORS_HEADERS,
    })
  }

  try {
    // Authenticate user
    const { user, error: authError } = await authenticateUser(req)
    if (authError || !user) {
      return createErrorResponse(authError || "Authentication failed", 401)
    }

    // Parse request body
    const { returnUrl } = await req.json()

    // Get user profile to find Stripe customer ID
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return createErrorResponse("User profile not found", 404)
    }

    if (!profile.stripe_customer_id) {
      return createErrorResponse("No Stripe customer found for this user", 400)
    }

    // Create Stripe Customer Portal Session
    const session = await stripe.billingPortal.sessions.create({
      customer: profile.stripe_customer_id,
      return_url: returnUrl || `${req.headers.get('origin')}/account`,
    })

    return createCorsResponse({
      url: session.url,
    })
  } catch (err) {
    console.error("Error creating portal session:", err)
    return createErrorResponse("Internal server error", 500)
  }
})