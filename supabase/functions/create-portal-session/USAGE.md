# Usage Example: Calling the Create Portal Session Function from JavaScript

This document provides examples of how to call the `create-portal-session` edge function from JavaScript.

## Example: Using `fetch` to create a Stripe Customer Portal Session

```javascript
// Replace with your actual function URL
const functionUrl = "https://your-supabase-project-url/functions/v1/create-portal-session";

// Replace with a valid Supabase user access token (JWT)
const accessToken = "YOUR_SUPABASE_USER_ACCESS_TOKEN";

// Optional: Replace with your actual return URL
// If not provided, the function will use the request origin header as fallback
const returnUrl = "https://yourdomain.com/account";

async function createPortalSession() {
  try {
    const response = await fetch(functionUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        returnUrl, // Optional: omit if you want to use origin header fallback
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    console.log("Portal URL:", data.url);

    // Redirect the user to the Stripe Customer Portal
    window.location.href = data.url;
  } catch (error) {
    console.error("Failed to create portal session:", error);
  }
}

// Call the function
createPortalSession();
```

## Example: Using fallback return URL (without explicit returnUrl)

```javascript
async function createPortalSessionWithFallback() {
  try {
    const response = await fetch(functionUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        // returnUrl omitted - will use origin header fallback
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    console.log("Portal URL:", data.url);

    // Redirect the user to the Stripe Customer Portal
    window.location.href = data.url;
  } catch (error) {
    console.error("Failed to create portal session:", error);
  }
}

// Call the function with fallback URL
createPortalSessionWithFallback();
```

## Notes

- Make sure the user is authenticated and you have a valid Supabase access token.
- Replace `functionUrl` with the actual deployed URL of your edge function.
- The function expects a JSON body with an optional `returnUrl` parameter.
- If `returnUrl` is not provided, the function will use the request's origin header as a fallback (`${origin}/account`).
- The user must have an existing Stripe customer ID in their profile (created when they first make a purchase).
- The response contains the Stripe Customer Portal URL (`url`) where users can manage their subscriptions and billing.

## Return URL Behavior (Optional)

The `returnUrl` parameter is optional. If provided in the JSON body, it tells Stripe where to redirect the user when they click "Return to [Your App]" in the customer portal. If not provided, the function will automatically generate a fallback URL using the request's origin header:

- **Return URL fallback:** `${origin}/account`

Example values when explicitly provided:

```javascript
const returnUrl = "https://yourdomain.com/account";
```

## Understanding `${req.headers.get('origin')}`

The `origin` header in the request indicates where the request is coming from. Here are typical values you might see depending on the client:

- **Production Web App:**
  `https://yourdomain.com`
  This is the domain where your production frontend is hosted.

- **Local Development (localhost):**
  `http://localhost:3000`
  This is common when running your frontend locally on port 3000.

- **Native iOS/Android Apps:**
  Native mobile apps often do not send an `origin` header, so `req.headers.get('origin')` may be `null` or `undefined`.
  In such cases, you might want to handle this scenario explicitly in your edge function.

Make sure your edge function logic accounts for these variations when constructing URLs or handling CORS.

## Error Handling

The function may return the following errors:

- **401 Unauthorized:** Invalid or missing authentication token
- **404 Not Found:** User profile not found in the database
- **400 Bad Request:** No Stripe customer found for this user (user hasn't made a purchase yet)
- **500 Internal Server Error:** Stripe API error or other server-side issues

## What is the Stripe Customer Portal?

The Stripe Customer Portal is a hosted solution that allows your customers to:

- View their subscription and payment history
- Update their payment methods
- Download invoices
- Cancel or modify their subscriptions
- Update their billing information

The portal is fully hosted by Stripe and provides a secure, PCI-compliant interface for customer self-service billing management.
