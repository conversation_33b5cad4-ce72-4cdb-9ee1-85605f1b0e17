import "jsr:@supabase/functions-js/edge-runtime.d.ts"

import type { Stripe as StripeTypes } from "https://esm.sh/stripe@14.21.0"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { createCorsResponse, createErrorResponse } from "../_shared/cors.ts"
import { stripe } from "../_shared/stripeClient.ts"

// Trial configuration
const TRIAL_DEFAULT = 7 // Default trial period in days
const USE_TRIAL_DEFAULT = true // Set to true to use default trial, false for no trial

// ─── Env vars ─────────────────────────────────────────────────────────────
const SP_SUPABASE_URL              = Deno.env.get("SP_SUPABASE_URL")!
const SP_SUPABASE_ANON_KEY         = Deno.env.get("SP_SUPABASE_ANON_KEY")!
const SP_SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SP_SUPABASE_SERVICE_ROLE_KEY")!

// ─── Clients ──────────────────────────────────────────────────────────────
const supabaseAuth  = createClient(SP_SUPABASE_URL, SP_SUPABASE_ANON_KEY)
const supabaseAdmin = createClient(SP_SUPABASE_URL, SP_SUPABASE_SERVICE_ROLE_KEY)

// ─── Auth helper ──────────────────────────────────────────────────────────
async function authenticate(req: Request) {
  const auth = req.headers.get("Authorization")?.split(" ")[1]
  if (!auth) return { user: null, error: "Missing Authorization header" }

  const { data: { user }, error } = await supabaseAuth.auth.getUser(auth)
  return { user, error: error?.message }
}


Deno.serve(async (req) => {
  // CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin":  "*",
        "Access-Control-Allow-Headers": "Authorization, apikey, Content-Type, x-client-info",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      },
    })
  }

  try {

    // Authenticate user
    const { user, error: authError } = await authenticate(req)
    if (authError || !user) {
      return createErrorResponse(authError || "Authentication failed", 401)
    }

    // Parse request body
    const { priceId, couponId, successUrl, cancelUrl, isCapacitor } = await req.json()

    if (!priceId) {
      return createErrorResponse("Price ID is required", 400)
    }

    // Get user profile to check for existing Stripe customer
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('stripe_customer_id, email, first_name, last_name')
      .eq('id', user.id)
      .single()

    let customerId = profile?.stripe_customer_id

    // Create Stripe customer if doesn't exist
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email || profile?.email,
        name: profile?.first_name && profile?.last_name 
          ? `${profile.first_name} ${profile.last_name}` 
          : undefined,
        metadata: {
          supabase_user_id: user.id,
        },
      })

      customerId = customer.id

      // Update profile with Stripe customer ID
      await supabaseAdmin
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', user.id)
    }

    // Check for existing active subscription
    const { data: existingSubscription } = await supabaseAdmin
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single()

    if (existingSubscription) {
      return createErrorResponse("User already has an active subscription", 400)
    }

    // Create Stripe Checkout Session
    const sessionConfig: StripeTypes.Checkout.SessionCreateParams = {
      customer: customerId,
      payment_method_types: ['card'],
      mode: 'subscription',
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      // success_url: successUrl || `${req.headers.get('origin')}/payment/ybd-app/success?session_id={CHECKOUT_SESSION_ID}`,
      // cancel_url: cancelUrl || `${req.headers.get('origin')}/payment/ybd-app/cancel`,
      success_url: successUrl || (isCapacitor 
        ? 'https://www.upliftingactions.com/payment/ybd-app/success?session_id={CHECKOUT_SESSION_ID}'
        : `${req.headers.get('origin')}/payment/ybd-app/success?session_id={CHECKOUT_SESSION_ID}`),
      cancel_url: cancelUrl || (isCapacitor 
        ? 'https://www.upliftingactions.com/payment/ybd-app/cancel'
        : `${req.headers.get('origin')}/payment/ybd-app/cancel`),
      client_reference_id: user.id, // This is the key part for reconciliation
      metadata: {
        user_id: user.id,
      },
      subscription_data: {
        metadata: {
          user_id: user.id,
        },
        ...(USE_TRIAL_DEFAULT && { trial_period_days: TRIAL_DEFAULT }),
      },
      // Enable coupon codes in checkout
      allow_promotion_codes: true,
    }

    // Add specific coupon if provided
    if (couponId) {
      sessionConfig.discounts = [{ coupon: couponId }]
    }

    const session = await stripe.checkout.sessions.create(sessionConfig)

    return createCorsResponse({
      url: session.url,
      sessionId: session.id,
    })
  } catch (err) {
    console.error("Error: creating-checkout-session:", err)
    return createErrorResponse("Internal server error", 500)
  }
})