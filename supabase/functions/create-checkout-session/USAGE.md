# Usage Example: Calling the Create Checkout Session Function from JavaScript

This document provides examples of how to call the `create-checkout-session` edge function from JavaScript.

## Example: Using `fetch` to create a Stripe Checkout Session

```javascript
// Replace with your actual function URL
const functionUrl = "https://your-supabase-project-url/functions/v1/create-checkout-session";

// Replace with a valid Supabase user access token (JWT)
const accessToken = "YOUR_SUPABASE_USER_ACCESS_TOKEN";

// The price ID for the Stripe subscription price you want to use
const priceId = "price_1234567890abcdef";

// Optional: coupon ID to apply a discount
const couponId = "coupon_abcdef1234567890";

// Replace with your actual success and cancel URLs
const successUrl = "https://yourdomain.com/success?session_id={CHECKOUT_SESSION_ID}";
const cancelUrl = "https://yourdomain.com/cancel";

async function createCheckoutSession() {
  try {
    const response = await fetch(functionUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        priceId,
        couponId, // Omit this line if you don't have a coupon
        successUrl,
        cancelUrl,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();
    console.log("Checkout URL:", data.url);
    console.log("Session ID:", data.sessionId);

    // Redirect the user to the Stripe Checkout page
    window.location.href = data.url;
  } catch (error) {
    console.error("Failed to create checkout session:", error);
  }
}

// Call the function
createCheckoutSession();
```

## Notes

- Make sure the user is authenticated and you have a valid Supabase access token.
- Replace `functionUrl` with the actual deployed URL of your edge function.
- The function expects a JSON body with a `priceId`, optionally a `couponId`, and now requires `successUrl` and `cancelUrl`.
- The response contains the Stripe Checkout session URL (`url`) and session ID (`sessionId`).

## Passing Success and Cancel URLs

In the example above, `successUrl` and `cancelUrl` are passed in the JSON body when creating the checkout session. These URLs tell Stripe where to redirect the user after a successful payment or cancellation.

Example values:

```javascript
const successUrl = "https://yourdomain.com/success?session_id={CHECKOUT_SESSION_ID}";
const cancelUrl = "https://yourdomain.com/cancel";
```

## Understanding `${req.headers.get('origin')}`

The `origin` header in the request indicates where the request is coming from. Here are typical values you might see depending on the client:

- **Production Web App:**
  `https://yourdomain.com`
  This is the domain where your production frontend is hosted.

- **Local Development (localhost):**
  `http://localhost:3000`
  This is common when running your frontend locally on port 3000.

- **Native iOS/Android Apps:**
  Native mobile apps often do not send an `origin` header, so `req.headers.get('origin')` may be `null` or `undefined`.
  In such cases, you might want to handle this scenario explicitly in your edge function.

Make sure your edge function logic accounts for these variations when constructing URLs or handling CORS.
