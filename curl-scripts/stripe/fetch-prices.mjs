// scripts/fetch-prices.js
import { writeFile } from 'fs/promises';
import Stripe from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET, {
  apiVersion: '2022-11-15',
});

async function fetchAndWrite() {
  if (!process.env.STRIPE_PRODUCT_ID) {
    throw new Error('Missing STRIPE_PRODUCT_ID in .env');
  }
  console.log('Fetching Stripe prices…');
  const prices = await stripe.prices.list({
    product: process.env.STRIPE_PRODUCT_ID,
    active: true,
    type: 'recurring',
    limit: 100,
  });

  const plans = prices.data.map(p => ({
    priceId:  p.id,
    nickname: p.nickname,
    interval: p.recurring.interval,
    interval_count: p.recurring.interval_count,
    amount:   p.unit_amount,
    currency: p.currency,
  }));

  await writeFile(
    new URL('../output/prices.json', import.meta.url),
    JSON.stringify(plans, null, 2)
  );
  console.log('✅ output/prices.json updated with', plans.length, 'plans');
}

fetchAndWrite().catch(err => {
  console.error(err);
  process.exit(1);
});

