#!/bin/bash

# Stripe Webhook Testing Script for Local Supabase
# This script helps you quickly test Stripe webhooks with your local setup

echo "🚀 Stripe Webhook Testing Script"
echo "================================"

# Check if Stripe CLI is installed
if ! command -v stripe &> /dev/null; then
    echo "❌ Stripe CLI is not installed!"
    echo "Install it with: brew install stripe/stripe-cli/stripe"
    exit 1
fi

# Check if Supabase is running
if ! curl -s http://localhost:54321/rest/v1/ > /dev/null; then
    echo "❌ Supabase doesn't seem to be running on port 54321"
    echo "Start it with: npx supabase start"
    exit 1
fi

echo "✅ Prerequisites checked"
echo ""

# Function to test webhook forwarding
test_webhook() {
    echo "📡 Starting Stripe webhook forwarding..."
    echo "Keep this terminal open and copy the webhook secret when it appears!"
    echo ""
    echo "Command: stripe listen --forward-to http://localhost:54321/functions/v1/stripe-webhook"
    echo ""
    echo "Once you see the webhook secret (whsec_test_xxx), you'll need to:"
    echo "1. Copy the secret"
    echo "2. Set it in your environment or .env.local file"
    echo "3. Open a new terminal to trigger test events"
    echo ""
    echo "Press Enter to start webhook forwarding..."
    read
    
    stripe listen --forward-to http://localhost:54321/functions/v1/stripe-webhook
}

# Function to trigger test events
trigger_events() {
    echo "🎯 Triggering test events..."
    echo ""
    
    echo "1. Testing checkout.session.completed..."
    stripe trigger checkout.session.completed
    sleep 2
    
    echo ""
    echo "2. Testing customer.subscription.created..."
    stripe trigger customer.subscription.created
    sleep 2
    
    echo ""
    echo "3. Testing invoice.payment_succeeded..."
    stripe trigger invoice.payment_succeeded
    
    echo ""
    echo "✅ Test events sent! Check your webhook forwarding terminal for results."
}

# Main menu
echo "What would you like to do?"
echo "1. Start webhook forwarding (run this first)"
echo "2. Trigger test events (run in a new terminal)"
echo "3. Show setup instructions"
echo ""
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        test_webhook
        ;;
    2)
        trigger_events
        ;;
    3)
        echo ""
        echo "📋 Setup Instructions:"
        echo "====================="
        echo ""
        echo "1. First, run this script and choose option 1 to start webhook forwarding"
        echo "2. Copy the webhook secret (whsec_test_xxx) that appears"
        echo "3. In a new terminal, set the secret:"
        echo "   export SP_STRIPE_WEBHOOK_SECRET='whsec_test_xxx'"
        echo "4. Run this script again in the new terminal and choose option 2"
        echo "5. Check the first terminal to see if webhooks are being received"
        echo ""
        echo "For persistent configuration, add to .env.local:"
        echo "SP_STRIPE_WEBHOOK_SECRET='whsec_test_xxx'"
        ;;
    *)
        echo "Invalid choice. Please run the script again."
        ;;
esac