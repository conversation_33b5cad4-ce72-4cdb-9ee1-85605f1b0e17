1. Deploy Edge Functions
bash# Deploy checkout session function
supabase functions deploy create-checkout-session

# Deploy webhook handler
supabase functions deploy stripe-webhook

# Deploy portal session function
supabase functions deploy create-portal-session

2. Set Environment Variables
In Supabase Dashboard → Settings → Edge Functions:

STRIPE_SECRET_KEY
SP_STRIPE_WEBHOOK_SECRET
SUPABASE_URL
SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY

3. Test the Flow

User visits /pricing
Clicks subscribe button
Redirected to Stripe Checkout
Completes payment
Redirected to /success
Webhook updates subscription in database