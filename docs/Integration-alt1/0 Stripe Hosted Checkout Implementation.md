# Stripe Hosted Checkout Implementation Guide for Svelte Capacitor App

## Overview
This guide implements Stripe Hosted Checkout for subscription payments in your Svelte Capacitor app with Supabase backend, using the `client_reference_id` to reconcile payments with users.

## IMPORTANT NOTE

IMPORTANT: Ignore all custom css in the examples in the documents in this folder. They are for example purposes and unfortunately do not follow the style of my actual app. Also note that I already have a good pricing component page in the app that is better than the example in this documentation. The examples here are mostly for edge function calls and to show what the edge functions will be/do

## Architecture Flow
1. **Frontend**: User clicks subscribe → Call Supabase Edge Function
2. **Edge Function**: Creates Stripe Checkout Session with `client_reference_id`
3. **Stripe**: User completes payment on <PERSON><PERSON>'s hosted page
4. **Webhook**: Updates user subscription in Supabase
5. **Success**: User returns to app with active subscription

---

## Database Setup for user profiles

Run this SQL in Supabase SQL Editor:

```sql
-- Create profiles table for stripe customer mapping
create table public.profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  stripe_customer_id text unique,
  email TEXT,
  first_name TEX<PERSON>,
  last_name TEX<PERSON>,
  early_adopter <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
  wizard_completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_subscriptions table
create table public.user_subscriptions (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users(id),
  subscription_id text unique,
  status text,
  created_at timestamp default now(),
  updated_at timestamp default now()
);

-- Enable RLS
alter table profiles enable row level security;
alter table user_subscriptions enable row level security;

-- RLS policies
create policy "Users can view own profile"
  on profiles for select
  using (auth.uid() = id);

create policy "Users can view own subscriptions"
  on user_subscriptions for select
  using (auth.uid() = user_id);

-- Create profile on signup
create or replace function public.handle_new_user()
  returns trigger
  language plpgsql
  security definer
  set search_path = public, pg_catalog
as $$
begin
  insert into public.profiles (id, email)
    values (new.id, new.email);
  return new;
end;
$$;

-- Attach trigger to auth.users
drop trigger if exists on_auth_user_created on auth.users;
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();



ALTER TABLE public.profiles 
DROP CONSTRAINT IF EXISTS profiles_id_fkey;

-- Add the foreign key with CASCADE DELETE
ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_id_fkey 
FOREIGN KEY (id) 
REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- Fix cascade delete for user_subscriptions table
-- First, drop the existing foreign key constraint
ALTER TABLE public.user_subscriptions 
DROP CONSTRAINT IF EXISTS user_subscriptions_user_id_fkey;

-- Add the foreign key with CASCADE DELETE
ALTER TABLE public.user_subscriptions 
ADD CONSTRAINT user_subscriptions_user_id_fkey 
FOREIGN KEY (user_id) 
REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- Optional: Add a trigger to handle cleanup on user deletion
-- This ensures any orphaned records are cleaned up
CREATE OR REPLACE FUNCTION public.handle_user_delete()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_catalog
AS $$
BEGIN
  -- Delete profile (this will happen automatically with CASCADE, but included for completeness)
  DELETE FROM public.profiles WHERE id = OLD.id;
  
  -- Delete subscriptions (this will happen automatically with CASCADE, but included for completeness)
  DELETE FROM public.user_subscriptions WHERE user_id = OLD.id;
  
  RETURN OLD;
END;
$$;

```



## 1. Supabase Edge Function Setup

### Create the Edge Function
```bash
supabase functions new create-checkout-session
```

### Edge Function Code (`supabase/functions/create-checkout-session/index.ts`)### Environment Variables
Add these to your Supabase project settings:

```bash
# In Supabase Dashboard → Settings → Edge Functions → Environment Variables
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

---

## 2. Stripe Webhook Handler

### Create Webhook Edge Function
```bash
supabase functions new stripe-webhook
```

### Webhook Handler Code (`supabase/functions/stripe-webhook/index.ts`)### Webhook Environment Variables
Add to Supabase:
```bash
SP_STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_from_stripe
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

---

## 3. Stripe Dashboard Configuration

### Create Webhook Endpoint
1. Go to Stripe Dashboard → Developers → Webhooks
2. Click "Add endpoint"
3. URL: `https://your-project.supabase.co/functions/v1/stripe-webhook`
4. Select these events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

### Get Webhook Secret
After creating the webhook, copy the webhook secret and add it to your Supabase environment variables.

---

## 4. Frontend Svelte Implementation

### Create Subscription Service### Pricing Component### Success Page---

## 5. Customer Portal Edge Function

### Create Portal Session Function---

## 6. Deployment Steps

### 1. Deploy Edge Functions
```bash
# Deploy checkout session function
supabase functions deploy create-checkout-session

# Deploy webhook handler
supabase functions deploy stripe-webhook

# Deploy portal session function
supabase functions deploy create-portal-session
```

### 2. Set Environment Variables
In Supabase Dashboard → Settings → Edge Functions:
- `STRIPE_SECRET_KEY`
- `SP_STRIPE_WEBHOOK_SECRET`
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

### 3. Test the Flow
1. User visits `/pricing`
2. Clicks subscribe button
3. Redirected to Stripe Checkout
4. Completes payment
5. Redirected to `/success`
6. Webhook updates subscription in database

---

## 7. Additional Features

### Subscription Status Hook (Optional)---

## 8. Testing

### Test Scenarios
1. **New User Subscription**: Sign up → Subscribe → Verify webhook → Check database
2. **Existing User**: Check subscription status → Show manage button
3. **Failed Payment**: Test with declined card → Verify webhook handles failure
4. **Subscription Cancellation**: Use customer portal → Verify webhook updates status

### Test Cards (Stripe)
- **Success**: `****************`
- **Decline**: `****************`
- **Insufficient Funds**: `****************`

---

## 9. Key Points

1. **client_reference_id** is automatically passed to webhooks for user reconciliation
2. **Stripe handles all payment UI** - no domain verification needed
3. **Webhooks ensure data consistency** between Stripe and Supabase
4. **Customer portal** allows users to manage their subscriptions
5. **Error handling** is built into all functions

This implementation provides a complete subscription system with proper user reconciliation using Stripe's `client_reference_id` feature!