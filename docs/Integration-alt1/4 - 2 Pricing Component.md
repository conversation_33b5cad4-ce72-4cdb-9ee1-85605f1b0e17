


```html
<!-- src/routes/pricing/+page.svelte -->
<script lang="ts">
  import { onMount } from 'svelte'
  import { supabase } from '$lib/supabase'
  import { SubscriptionService } from '$lib/services/subscriptionService'
  import type { User } from '@supabase/supabase-js'

  let user: User | null = null
  let loading = true
  let subscriptionStatus: any = null
  let subscribing = false

  // Your Stripe price IDs
  const PRICE_IDS = {
    monthly: 'price_1234567890abcdef', // Replace with your actual price IDs
    yearly: 'price_0987654321fedcba'
  }

  onMount(async () => {
    // Get current user
    const { data: { user: currentUser } } = await supabase.auth.getUser()
    user = currentUser

    if (user) {
      // Check subscription status
      subscriptionStatus = await SubscriptionService.checkSubscriptionStatus(user)
    }
    
    loading = false
  })

  async function handleSubscribe(priceId: string) {
    if (!user) {
      // Redirect to sign in
      window.location.href = '/auth/signin'
      return
    }

    if (subscriptionStatus.hasActiveSubscription) {
      alert('You already have an active subscription!')
      return
    }

    try {
      subscribing = true
      await SubscriptionService.createCheckoutSession(priceId, user)
    } catch (error) {
      console.error('Subscription error:', error)
      alert('Failed to start subscription process. Please try again.')
    } finally {
      subscribing = false
    }
  }

  async function handleManageSubscription() {
    if (!user) return

    try {
      const portalUrl = await SubscriptionService.createPortalSession(user)
      window.location.href = portalUrl
    } catch (error) {
      console.error('Portal error:', error)
      alert('Failed to open customer portal. Please try again.')
    }
  }
</script>

<div class="pricing-container">
  <h1>Choose Your Plan</h1>
  
  {#if loading}
    <div class="loading">Loading...</div>
  {:else if subscriptionStatus?.hasActiveSubscription}
    <div class="current-plan">
      <h2>Current Plan</h2>
      <p>You have an active subscription</p>
      <p>Status: {subscriptionStatus.subscription.status}</p>
      <button on:click={handleManageSubscription} class="manage-btn">
        Manage Subscription
      </button>
    </div>
  {:else}
    <div class="plans">
      <div class="plan">
        <h3>Monthly Plan</h3>
        <p class="price">$9.99/month</p>
        <ul>
          <li>All features included</li>
          <li>Cancel anytime</li>
          <li>Email support</li>
        </ul>
        <button 
          on:click={() => handleSubscribe(PRICE_IDS.monthly)}
          disabled={subscribing}
          class="subscribe-btn"
        >
          {subscribing ? 'Processing...' : 'Subscribe Monthly'}
        </button>
      </div>

      <div class="plan featured">
        <h3>Yearly Plan</h3>
        <p class="price">$99.99/year</p>
        <p class="savings">Save 2 months!</p>
        <ul>
          <li>All features included</li>
          <li>Cancel anytime</li>
          <li>Priority email support</li>
          <li>Annual billing</li>
        </ul>
        <button 
          on:click={() => handleSubscribe(PRICE_IDS.yearly)}
          disabled={subscribing}
          class="subscribe-btn featured"
        >
          {subscribing ? 'Processing...' : 'Subscribe Yearly'}
        </button>
      </div>
    </div>
  {/if}
</div>

<style>
  .pricing-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  .loading {
    text-align: center;
    padding: 2rem;
  }

  .current-plan {
    text-align: center;
    padding: 2rem;
    border: 2px solid #4CAF50;
    border-radius: 8px;
    background-color: #f0f8f0;
  }

  .plans {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .plan {
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease;
  }

  .plan:hover {
    transform: translateY(-5px);
  }

  .plan.featured {
    border-color: #4CAF50;
    background-color: #f8fff8;
  }

  .price {
    font-size: 2rem;
    font-weight: bold;
    color: #4CAF50;
    margin: 1rem 0;
  }

  .savings {
    color: #ff6b6b;
    font-weight: bold;
    margin-bottom: 1rem;
  }

  .subscribe-btn, .manage-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 1rem;
    transition: background-color 0.3s ease;
  }

  .subscribe-btn:hover, .manage-btn:hover {
    background-color: #45a049;
  }

  .subscribe-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }

  .subscribe-btn.featured {
    background-color: #ff6b6b;
  }

  .subscribe-btn.featured:hover {
    background-color: #ff5252;
  }

  ul {
    list-style: none;
    padding: 0;
  }

  li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
  }

  li:last-child {
    border-bottom: none;
  }
</style>
```