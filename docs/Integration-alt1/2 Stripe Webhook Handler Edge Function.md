## 2. Stripe Webhook Handler

### Create Webhook Edge Function
```bash
supabase functions new stripe-webhook
```

### Webhook Handler Code (`supabase/functions/stripe-webhook/index.ts`)

### Webhook Environment Variables

Add related Supabase secrets to supabase secrets dashboard for edge functions
Double check function code for any other env vars.

# How to Fix 401 "Missing Authorization Header" Error for Stripe Webhook on Supabase Edge Function

When deploying a Stripe webhook as a Supabase Edge Function, you may encounter a 401 error with the message:

```
{
  "code": 401,
  "message": "Missing authorization header"
}
```

This happens because the Supabase Edge Function is configured to require a JWT authorization header by default, but Stripe webhook calls do not include this header.

## How to Fix

1. Go to the Supabase Dashboard.
2. Navigate to the **Functions** section.
3. Find and select your `stripe-webhook` Edge Function.
4. On the function's **Details** page, locate the setting labeled **Verify JWT with legacy secret**.
5. Disable this setting (turn it OFF).

   - This setting requires a JWT signed by the legacy JWT secret in the `Authorization` header.
   - Disabling it allows the function to be called without an authorization header.
   - It is recommended to keep this OFF if you implement your own authorization logic inside the function code.

6. Save the changes.

## Additional Notes

- Disabling JWT verification here does not affect your local `config.toml` file.
- You should implement any necessary security or verification (e.g., Stripe webhook signature verification) inside your function code.
- This approach allows Stripe to call your webhook endpoint without sending an authorization header, preventing the 401 error.

---

This summarizes the necessary step to fix the 401 error caused by missing authorization header for Stripe webhook calls to Supabase Edge Functions.