

```html
<!-- src/routes/success/+page.svelte -->
<script lang="ts">
  import { onMount } from 'svelte'
  import { page } from '$app/stores'
  import { supabase } from '$lib/supabase'
  import { SubscriptionService } from '$lib/services/subscriptionService'

  let loading = true
  let sessionId: string | null = null
  let subscriptionStatus: any = null
  let user: any = null

  onMount(async () => {
    // Get session ID from URL
    sessionId = $page.url.searchParams.get('session_id')
    
    // Get current user
    const { data: { user: currentUser } } = await supabase.auth.getUser()
    user = currentUser

    if (user) {
      // Check updated subscription status
      subscriptionStatus = await SubscriptionService.checkSubscriptionStatus(user)
    }

    loading = false
  })

  function goToApp() {
    window.location.href = '/app' // or wherever your main app is
  }

  function goToPricing() {
    window.location.href = '/pricing'
  }
</script>

<div class="success-container">
  {#if loading}
    <div class="loading">
      <div class="spinner"></div>
      <p>Confirming your subscription...</p>
    </div>
  {:else if subscriptionStatus?.hasActiveSubscription}
    <div class="success">
      <div class="check-icon">✓</div>
      <h1>Welcome to Premium!</h1>
      <p>Your subscription has been activated successfully.</p>
      
      <div class="subscription-details">
        <h3>Subscription Details</h3>
        <p><strong>Status:</strong> {subscriptionStatus.subscription.status}</p>
        <p><strong>Started:</strong> {new Date(subscriptionStatus.subscription.created_at).toLocaleDateString()}</p>
        {#if sessionId}
          <p><strong>Session ID:</strong> {sessionId}</p>
        {/if}
      </div>

      <div class="actions">
        <button on:click={goToApp} class="primary-btn">
          Start Using Premium Features
        </button>
        <button on:click={goToPricing} class="secondary-btn">
          View Pricing
        </button>
      </div>
    </div>
  {:else}
    <div class="pending">
      <h2>Processing Your Subscription</h2>
      <p>Your payment was successful, but we're still setting up your subscription.</p>
      <p>This usually takes just a few moments. You'll receive an email confirmation shortly.</p>
      
      <div class="actions">
        <button on:click={() => window.location.reload()} class="primary-btn">
          Check Again
        </button>
        <button on:click={goToPricing} class="secondary-btn">
          Back to Pricing
        </button>
      </div>
    </div>
  {/if}
</div>

<style>
  .success-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
  }

  .loading {
    padding: 4rem 2rem;
  }

  .spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .success {
    padding: 2rem;
  }

  .check-icon {
    font-size: 4rem;
    color: #4CAF50;
    margin-bottom: 1rem;
  }

  .subscription-details {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
    text-align: left;
  }

  .subscription-details h3 {
    margin-top: 0;
    color: #333;
  }

  .actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
  }

  .primary-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
  }

  .primary-btn:hover {
    background-color: #45a049;
  }

  .secondary-btn {
    background-color: transparent;
    color: #4CAF50;
    border: 2px solid #4CAF50;
    padding: 1rem 2rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
  }

  .secondary-btn:hover {
    background-color: #4CAF50;
    color: white;
  }

  .pending {
    padding: 2rem;
    background-color: #fff3cd;
    border: 2px solid #ffeaa7;
    border-radius: 8px;
  }

  .pending h2 {
    color: #856404;
  }

  .pending p {
    color: #856404;
  }

  @media (max-width: 768px) {
    .actions {
      flex-direction: column;
    }
    
    .primary-btn, .secondary-btn {
      width: 100%;
    }
  }
</style>
```