```ts
// src/lib/stores/subscription.ts
import { writable } from 'svelte/store'
import { supabase } from '$lib/supabase'
import { SubscriptionService } from '$lib/services/subscriptionService'

export interface SubscriptionState {
  hasActiveSubscription: boolean
  subscription: any
  loading: boolean
  error: string | null
}

const initialState: SubscriptionState = {
  hasActiveSubscription: false,
  subscription: null,
  loading: true,
  error: null
}

export const subscriptionStore = writable<SubscriptionState>(initialState)

export const subscriptionActions = {
  async checkStatus() {
    subscriptionStore.update(state => ({ ...state, loading: true, error: null }))
    
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        subscriptionStore.set({
          hasActiveSubscription: false,
          subscription: null,
          loading: false,
          error: null
        })
        return
      }

      const status = await SubscriptionService.checkSubscriptionStatus(user)
      
      subscriptionStore.update(state => ({
        ...state,
        hasActiveSubscription: status.hasActiveSubscription,
        subscription: status.subscription,
        loading: false
      }))
    } catch (error) {
      subscriptionStore.update(state => ({
        ...state,
        loading: false,
        error: error.message
      }))
    }
  },

  async subscribe(priceId: string) {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')
      
      await SubscriptionService.createCheckoutSession(priceId, user)
    } catch (error) {
      subscriptionStore.update(state => ({
        ...state,
        error: error.message
      }))
      throw error
    }
  },

  async openPortal() {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')
      
      const url = await SubscriptionService.createPortalSession(user)
      window.location.href = url
    } catch (error) {
      subscriptionStore.update(state => ({
        ...state,
        error: error.message
      }))
      throw error
    }
  }
}

// Initialize on auth state change
supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
    subscriptionActions.checkStatus()
  } else if (event === 'SIGNED_OUT') {
    subscriptionStore.set(initialState)
  }
})
```