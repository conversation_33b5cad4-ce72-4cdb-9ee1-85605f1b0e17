

```ts
// src/lib/services/subscriptionService.ts
import { supabase } from '$lib/supabase'
import type { User } from '@supabase/supabase-js'

export interface SubscriptionStatus {
  hasActiveSubscription: boolean
  subscription: any
  stripeCustomerId: string | null
}

export class SubscriptionService {
  /**
   * Check if user has an active subscription
   */
  static async checkSubscriptionStatus(user: User): Promise<SubscriptionStatus> {
    try {
      // Get user profile and subscription in one query
      const { data: profile } = await supabase
        .from('profiles')
        .select(`
          stripe_customer_id,
          user_subscriptions (
            id,
            subscription_id,
            status,
            created_at,
            updated_at
          )
        `)
        .eq('id', user.id)
        .single()

      const activeSubscription = profile?.user_subscriptions?.find(
        (sub: any) => sub.status === 'active'
      )

      return {
        hasActiveSubscription: !!activeSubscription,
        subscription: activeSubscription,
        stripeCustomerId: profile?.stripe_customer_id || null
      }
    } catch (error) {
      console.error('Error checking subscription status:', error)
      return {
        hasActiveSubscription: false,
        subscription: null,
        stripeCustomerId: null
      }
    }
  }

  /**
   * Create checkout session and redirect to Stripe
   */
  static async createCheckoutSession(priceId: string, user: User): Promise<void> {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        throw new Error('No active session')
      }

      const response = await supabase.functions.invoke('create-checkout-session', {
        body: {
          priceId,
          successUrl: `${window.location.origin}/success`,
          cancelUrl: `${window.location.origin}/pricing`
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`
        }
      })

      if (response.error) {
        throw new Error(response.error.message)
      }

      // Redirect to Stripe Checkout
      window.location.href = response.data.url
    } catch (error) {
      console.error('Error creating checkout session:', error)
      throw error
    }
  }

  /**
   * Generate customer portal link for subscription management
   */
  static async createPortalSession(user: User): Promise<string> {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        throw new Error('No active session')
      }

      const response = await supabase.functions.invoke('create-portal-session', {
        body: {
          returnUrl: `${window.location.origin}/account`
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`
        }
      })

      if (response.error) {
        throw new Error(response.error.message)
      }

      return response.data.url
    } catch (error) {
      console.error('Error creating portal session:', error)
      throw error
    }
  }

  /**
   * Get all subscriptions for a user
   */
  static async getUserSubscriptions(user: User) {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error getting user subscriptions:', error)
      throw error
    }
  }
}
```