# Edge Function Import Fix Documentation

## Issue
The create-subscription edge function was failing with the error:
```
worker boot error: failed to create the graph: Relative import path "@supabase/supabase-js" not prefixed with / or ./ or ../ and not in import map
```

## Root Cause
Supabase Edge Functions use Deno runtime, which requires either:
1. Full URLs for external module imports
2. Properly configured import maps

While an import map was present at `supabase/functions/import_map.json`, it wasn't being properly loaded in some environments.

## Solution
Updated all imports to use direct ESM URLs instead of bare module specifiers:

### Files Modified

1. **supabase/functions/create-subscription/index.ts**
   - Changed: `import { createClient } from "@supabase/supabase-js"`
   - To: `import { createClient } from "https://esm.sh/@supabase/supabase-js@2"`
   - Changed: `import Stripe from "stripe"`
   - To: `import Stripe from "https://esm.sh/stripe@14.21.0"`

2. **supabase/functions/_shared/supabaseAdmin.ts**
   - Changed: `import { createClient } from '@supabase/supabase-js'`
   - To: `import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'`

3. **supabase/functions/_shared/stripeClient.ts**
   - Changed: `import Stripe from 'stripe'`
   - To: `import Stripe from 'https://esm.sh/stripe@14.21.0'`

## Benefits
- Ensures compatibility across all environments (local and production)
- Removes dependency on import map configuration
- More explicit about exact versions being used
- Follows Deno best practices for external dependencies

## Testing
After these changes, the edge functions should work properly. Test by running:
```bash
# Start Supabase locally
supabase start

# Test the function
curl -X POST http://localhost:54321/functions/v1/create-subscription \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"priceId": "price_1234567890"}'