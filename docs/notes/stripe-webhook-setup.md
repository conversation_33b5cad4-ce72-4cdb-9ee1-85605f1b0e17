# Stripe Webhook Configuration Guide

## Why the 406 Error and Empty Table?

The 406 error when querying `user_subscriptions` combined with an empty table after payment indicates that:
1. The table exists with proper RLS policies
2. But no subscription data is being synced from Stripe
3. This is because the webhook endpoint hasn't been configured in Stripe

## Setting Up the Webhook in Stripe Dashboard

### 1. Get Your Webhook Endpoint URL

Your webhook endpoint URL will be:
```
https://YOUR_PROJECT_REF.supabase.co/functions/v1/stripe-webhook
```

To find your project reference:
- Go to your Supabase dashboard
- Look at the URL or project settings
- The project ref is the unique identifier for your project

### 2. Configure in Stripe Dashboard

1. **Log into your Stripe Dashboard** (test mode)
   - Go to https://dashboard.stripe.com/test/webhooks
   - https://dashboard.stripe.com/test/workbench/webhook

2. **Add Endpoint**
   - Click "Add endpoint"
   - Enter your endpoint URL: `https://YOUR_PROJECT_REF.supabase.co/functions/v1/stripe-webhook`
   - Select events to listen to:
     - `checkout.session.completed` (**CRITICAL** - this creates the initial subscription record)
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`

3. **Copy the Signing Secret**
   - After creating the endpoint, click on it
   - Reveal and copy the "Signing secret" (starts with `whsec_`)

### 3. Set the Webhook Secret in Supabase

```bash
# Using Supabase CLI
npx supabase secrets set SP_STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret_here"

# Or set it in your Supabase dashboard under Edge Functions > Secrets
```

### 4. Deploy Your Edge Function (if not already deployed)

```bash
npx supabase functions deploy stripe-webhook
```

## Testing the Webhook

### Option 1: Using Stripe CLI (Recommended for Local Testing)

```bash
# Install Stripe CLI
brew install stripe/stripe-cli/stripe

# Login to your Stripe account
stripe login

# Forward events to your local or deployed function
stripe listen --forward-to https://YOUR_PROJECT_REF.supabase.co/functions/v1/stripe-webhook

# In another terminal, trigger a test event
stripe trigger checkout.session.completed
```

### Option 2: Make a Real Test Payment

1. Go through your checkout flow
2. Use test card: `4242 4242 4242 4242`
3. Check Stripe Dashboard > Webhooks to see if events were sent
4. Check your Edge Function logs

## Verifying It's Working

1. **Check Stripe Dashboard**
   - Go to Webhooks section
   - Click on your endpoint
   - Look at "Recent deliveries" to see if events are being sent

2. **Check Edge Function Logs**
   ```bash
   npx supabase functions logs stripe-webhook
   ```

3. **Query the Database**
   - After a successful payment, check if `user_subscriptions` table has data
   - The subscription should be created with status 'active'

## Common Issues

1. **Webhook Secret Mismatch**
   - Make sure the secret in Supabase matches the one from Stripe exactly

2. **Wrong Endpoint URL**
   - Double-check your project reference
   - Ensure you're using the correct URL format

3. **Missing Events**
   - Make sure you selected all necessary events in Stripe

4. **Function Not Deployed**
   - Ensure your stripe-webhook function is deployed to Supabase

## After Setup

Once the webhook is configured:
1. The `user_subscriptions` table will be populated automatically
2. The 406 error should resolve as the table will have data
3. Your subscription queries will work properly