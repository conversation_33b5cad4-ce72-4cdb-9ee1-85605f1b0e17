# Stripe Webhook Testing with Local Supabase Docker

## Overview

When running Supabase locally via Docker, Stripe's cloud services cannot directly reach your local webhook endpoint. This guide provides multiple solutions for testing Stripe webhooks with your local development environment.

## Option 1: Stripe CLI Webhook Forwarding (Recommended)

The Stripe CLI can forward webhook events from Stripe to your local Supabase instance.

### Installation

```bash
# macOS
brew install stripe/stripe-cli/stripe

# Windows (using scoop)
scoop bucket add stripe https://github.com/stripe/scoop-stripe-cli.git
scoop install stripe

# Linux
# Download from https://github.com/stripe/stripe-cli/releases
```

### Setup and Usage

1. **Login to Stripe CLI**
   ```bash
   stripe login
   ```

2. **Start webhook forwarding to your local Supabase**
   ```bash
   # Forward to your local Supabase Docker instance
   stripe listen --forward-to http://localhost:54321/functions/v1/stripe-webhook
   ```

3. **Copy the webhook signing secret**
   The CLI will output a webhook signing secret like:
   ```
   Ready! Your webhook signing secret is whsec_test_xxxxx
   ```

4. **Set the webhook secret in your local environment**
   ```bash
   # Set it as an environment variable for your edge functions
   export SP_STRIPE_WEBHOOK_SECRET="whsec_test_xxxxx"
   
   # Or add it to your .env.local file
   echo 'SP_STRIPE_WEBHOOK_SECRET="whsec_test_xxxxx"' >> .env.local
   ```

5. **Trigger test events**
   In a new terminal:
   ```bash
   # Trigger a checkout session completed event
   stripe trigger checkout.session.completed
   
   # Trigger a subscription created event
   stripe trigger customer.subscription.created
   
   # List all available events
   stripe trigger --help
   ```

### Testing with Real Payments

1. Keep the Stripe CLI running with webhook forwarding
2. Make a test payment through your application
3. The CLI will capture and forward the real events to your local instance

## Option 2: ngrok Tunnel (Alternative)

If you prefer to expose your local instance to the internet temporarily:

### Installation

```bash
# macOS
brew install ngrok

# Or download from https://ngrok.com/download
```

### Setup

1. **Start ngrok tunnel**
   ```bash
   ngrok http 54321
   ```

2. **Copy the HTTPS URL**
   ngrok will provide a URL like: `https://abc123.ngrok.io`

3. **Configure webhook in Stripe Dashboard**
   - Go to https://dashboard.stripe.com/test/webhooks
   - Add endpoint: `https://abc123.ngrok.io/functions/v1/stripe-webhook`
   - Select events to listen for
   - Copy the signing secret

4. **Set the webhook secret locally**
   ```bash
   export SP_STRIPE_WEBHOOK_SECRET="whsec_xxxxx"
   ```

## Option 3: Deploy to Production Supabase

For testing with actual production-like environment:

### Prerequisites

1. **Create a Supabase project** at https://supabase.com
2. **Get your project reference** from the project URL

### Deployment Steps

1. **Set production secrets**
   ```bash
   # Set your production Stripe keys
   npx supabase secrets set STRIPE_SECRET_KEY="sk_test_xxxxx" --project-ref your-project-ref
   npx supabase secrets set SP_STRIPE_WEBHOOK_SECRET="whsec_xxxxx" --project-ref your-project-ref
   ```

2. **Deploy edge functions**
   ```bash
   # Deploy all functions
   npx supabase functions deploy --project-ref your-project-ref
   
   # Or deploy specific function
   npx supabase functions deploy stripe-webhook --project-ref your-project-ref
   ```

3. **Configure webhook in Stripe Dashboard**
   - Go to https://dashboard.stripe.com/test/webhooks
   - Add endpoint: `https://your-project-ref.supabase.co/functions/v1/stripe-webhook`
   - Select required events:
     - `checkout.session.completed`
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`

## Debugging Webhook Issues

### Check Stripe CLI Output
When using Stripe CLI forwarding, you'll see real-time logs:
```bash
2024-01-11 09:00:00   --> checkout.session.completed [evt_xxxxx]
2024-01-11 09:00:01  <--  [200] POST http://localhost:54321/functions/v1/stripe-webhook
```

### Check Supabase Function Logs
```bash
# For local Docker
docker logs supabase_functions_stripe-webhook

# For production
npx supabase functions logs stripe-webhook --project-ref your-project-ref
```

### Verify Database Updates
```sql
-- Check if subscription was created
SELECT * FROM user_subscriptions WHERE user_id = 'your-user-id';

-- Check webhook event logs (if you're logging them)
SELECT * FROM stripe_webhook_events ORDER BY created_at DESC LIMIT 10;
```

### Common Issues and Solutions

1. **Webhook signature verification fails**
   - Ensure the webhook secret matches exactly
   - Check that you're using the correct secret for the environment

2. **404 Not Found**
   - Verify the function is deployed: `npx supabase functions list`
   - Check the endpoint URL is correct

3. **CORS errors**
   - Webhooks don't use CORS, but ensure your function handles POST requests

4. **Database not updating**
   - Check function has proper service role key access
   - Verify RLS policies allow service role to insert/update

## Testing Checklist

- [ ] Stripe CLI installed and logged in
- [ ] Webhook forwarding started
- [ ] Webhook secret set in environment
- [ ] Test event triggered successfully
- [ ] Function logs show incoming webhook
- [ ] Database tables updated correctly
- [ ] Application reflects subscription status

## Production Deployment Checklist

- [ ] Production Stripe keys set as secrets
- [ ] Webhook endpoint configured in Stripe Dashboard
- [ ] All required events selected
- [ ] Webhook signing secret set in Supabase
- [ ] Edge functions deployed
- [ ] Test payment processed successfully
- [ ] Monitoring and alerting configured