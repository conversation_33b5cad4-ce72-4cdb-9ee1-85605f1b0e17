# Stripe Webhook Testing Guide for Local Supabase Docker

## The Problem
- You have Stripe sandbox in the cloud
- You have Supabase running locally in Docker
- Cloud services can't reach your local Docker instance
- You need to test webhooks during development

## Solution Overview

Since Stripe's cloud services cannot directly communicate with your local Supabase Docker instance, you have three main options:

### Option 1: Stripe CLI Forwarding (Recommended) ✅
Use Stripe CLI to forward webhook events from Stripe to your local instance.

### Option 2: ngrok Tunnel
Expose your local instance to the internet temporarily using ngrok.

### Option 3: Deploy to Production
Deploy your functions to a production Supabase instance for testing.

## Quick Start with Stripe CLI

### 1. Install Stripe CLI
```bash
# macOS
brew install stripe/stripe-cli/stripe

# Windows
scoop bucket add stripe https://github.com/stripe/scoop-stripe-cli.git
scoop install stripe
```

### 2. Start Webhook Forwarding
```bash
# Terminal 1: Start forwarding
stripe login
stripe listen --forward-to http://localhost:54321/functions/v1/stripe-webhook
```

**Important:** Copy the webhook secret that appears:
```
Ready! Your webhook signing secret is whsec_test_xxxxxxxxxxxxx
```

### 3. Configure Your Environment
```bash
# Terminal 2: Set the webhook secret
export SP_STRIPE_WEBHOOK_SECRET="whsec_test_xxxxxxxxxxxxx"

# Or add to .env.local for persistence
echo 'SP_STRIPE_WEBHOOK_SECRET="whsec_test_xxxxxxxxxxxxx"' >> .env.local
```

### 4. Test the Webhook
```bash
# Terminal 2: Trigger test events
stripe trigger checkout.session.completed
stripe trigger customer.subscription.created
```

### 5. Verify Success
- Check Terminal 1 for incoming webhook logs
- Query your database for new subscriptions
- Check edge function logs if needed

## Using the Test Script

We've included a helper script to make testing easier:

```bash
# Make it executable (if allowed)
chmod +x test-stripe-webhook.sh

# Run the script
./test-stripe-webhook.sh

# Or run with bash directly
bash test-stripe-webhook.sh
```

## File Structure

```
docs/
├── stripe-webhook-setup.md              # Original setup guide
├── stripe-webhook-local-testing.md      # Detailed local testing guide
├── stripe-webhook-quick-start.md        # Quick reference
└── STRIPE_WEBHOOK_TESTING_README.md     # This file

test-stripe-webhook.sh                   # Helper script for testing
```

## Common Issues

### 1. Webhook Signature Verification Failed
- Ensure you're using the exact secret from Stripe CLI
- Check that your edge function reads `SP_STRIPE_WEBHOOK_SECRET`

### 2. Connection Refused
- Verify Supabase is running: `docker ps`
- Check the URL: `http://localhost:54321/functions/v1/stripe-webhook`

### 3. Database Not Updating
- Check function logs: `docker logs -f supabase_functions_stripe-webhook`
- Verify service role key is configured
- Check RLS policies

## Testing Workflow

1. **Start Stripe CLI forwarding** (Terminal 1)
2. **Set webhook secret** (Terminal 2)
3. **Trigger test events** or make real test payments
4. **Monitor logs** in Terminal 1
5. **Verify database updates**

## Production Deployment

Once local testing is complete:

1. Deploy to production Supabase:
   ```bash
   npx supabase functions deploy stripe-webhook --project-ref your-ref
   ```

2. Configure webhook in Stripe Dashboard:
   - URL: `https://your-project-ref.supabase.co/functions/v1/stripe-webhook`
   - Events: checkout.session.completed, customer.subscription.*, invoice.*

3. Set production secrets:
   ```bash
   npx supabase secrets set SP_STRIPE_WEBHOOK_SECRET="whsec_live_xxx" --project-ref your-ref
   ```

## Resources

- [Stripe CLI Documentation](https://stripe.com/docs/stripe-cli)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [Stripe Webhooks Guide](https://stripe.com/docs/webhooks)

## Need Help?

1. Check the detailed guides in the `docs/` folder
2. Review edge function logs
3. Verify your Stripe Dashboard webhook configuration
4. Ensure all environment variables are set correctly