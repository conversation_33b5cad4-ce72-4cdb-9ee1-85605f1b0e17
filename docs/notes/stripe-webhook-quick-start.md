# Quick Start: Testing Stripe Webhooks with Local Supabase

## Prerequisites
- Local Supabase running on Docker (port 54321)
- Stripe test API keys configured
- Stripe CLI installed

## Step-by-Step Guide

### 1. Install Stripe CLI (if not already installed)
```bash
# macOS
brew install stripe/stripe-cli/stripe

# Verify installation
stripe --version
```

### 2. Login to Stripe
```bash
stripe login
```

### 3. Start Webhook Forwarding
```bash
# This forwards Stripe events to your local Supabase
stripe listen --forward-to http://localhost:54321/functions/v1/stripe-webhook
```

**Important:** Keep this terminal window open! Copy the webhook signing secret that appears:
```
Ready! Your webhook signing secret is whsec_test_xxxxxxxxxxxxx
```

### 4. Set the Webhook Secret

In a new terminal window:
```bash
# Option A: Set as environment variable (temporary)
export SP_STRIPE_WEBHOOK_SECRET="whsec_test_xxxxxxxxxxxxx"

# Option B: Add to your .env.local file (persistent)
echo 'SP_STRIPE_WEBHOOK_SECRET="whsec_test_xxxxxxxxxxxxx"' >> .env.local
```

### 5. Update Your Edge Function

Make sure your `stripe-webhook/index.ts` uses the webhook secret:
```typescript
const webhookSecret = Deno.env.get('SP_STRIPE_WEBHOOK_SECRET')!;
```

### 6. Test the Webhook

In another terminal:
```bash
# Test with a simulated event
stripe trigger checkout.session.completed

# Or trigger multiple events
stripe trigger customer.subscription.created
stripe trigger invoice.payment_succeeded
```

### 7. Verify It's Working

Check the Stripe CLI terminal - you should see:
```
2024-01-11 09:00:00   --> checkout.session.completed [evt_xxxxx]
2024-01-11 09:00:01  <--  [200] POST http://localhost:54321/functions/v1/stripe-webhook
```

Check your database:
```sql
-- Connect to your local Supabase
-- Check if subscriptions are being created
SELECT * FROM user_subscriptions ORDER BY created_at DESC;
```

## Testing with Real Checkout Flow

1. Keep Stripe CLI forwarding running
2. Go through your actual checkout process
3. Use test card: `4242 4242 4242 4242`
4. Watch the Stripe CLI terminal for real events
5. Check your database for the created subscription

## Troubleshooting

### "Webhook signature verification failed"
- Make sure you copied the exact webhook secret from Stripe CLI
- Ensure your edge function is reading the correct environment variable

### "Connection refused" or 404 errors
- Verify Supabase is running: `docker ps`
- Check the function exists: `npx supabase functions list`
- Ensure the URL is exactly: `http://localhost:54321/functions/v1/stripe-webhook`

### Database not updating
- Check edge function logs: `docker logs -f supabase_functions_stripe-webhook`
- Verify your function has the service role key configured
- Check RLS policies on your tables

## Next Steps

Once local testing is working:
1. Deploy your edge functions to production Supabase
2. Configure the production webhook in Stripe Dashboard
3. Update production secrets with real webhook signing secret

## Useful Commands

```bash
# View all available test events
stripe trigger --help

# See recent webhook attempts
stripe events list

# Replay a specific event
stripe events resend evt_xxxxxxxxxxxxx

# Stop webhook forwarding
# Press Ctrl+C in the stripe listen terminal