<script lang="ts">
    import { goto } from '$app/navigation';
    import PremiumPrompt from '$lib/components/PremiumPrompt.svelte';
    
    interface EmotionOption {
        label: string;
        emoji: string;
        description: string;
        action: () => void;
    }
    
    const emotions: EmotionOption[] = [
        {
            label: 'Anger',
            emoji: '😠',
            description: 'Release frustration and find peace',
            action: () => handleEmotionSelect('anger')
        },
        {
            label: 'Sadness',
            emoji: '😢',
            description: 'Process grief and find comfort',
            action: () => handleEmotionSelect('sadness')
        },
        {
            label: 'Worry/Fear',
            emoji: '😰',
            description: 'Calm anxiety and find courage',
            action: () => handleEmotionSelect('fear')
        },
        {
            label: 'Visions from Past',
            emoji: '⬅️',
            description: 'Release yourself from the contradictory burden of history.',
            action: () => handleEmotionSelect('past')
        },
        {
            label: 'Visions of Future',
            emoji: '➡️',
            description: 'Transform future concerns into future hope in a world of chaos.',
            action: () => handleEmotionSelect('future')
        }
    ];
    
    let selectedEmotion: string | null = null;
    let showResponse = false;
    
    function handleEmotionSelect(emotion: string) {
        selectedEmotion = emotion;
        showResponse = true;
        
        // In the future, this could navigate to specific content
        // or show tailored responses based on the emotion
    }
    
    function resetSelection() {
        selectedEmotion = null;
        showResponse = false;
    }
</script>

<div class="text-center">
    <p class="text-foreground/80 mb-8 max-w-2xl mx-auto">
        You have the power to transform your mental state. Use the free tool below to help remind you of your power and choices.
    </p>

    <p class="text-foreground/80 mb-8 max-w-2xl mx-auto">
        Choose what's bothering you and take a step toward freedom and personal agency in any situation.
    </p>
    
    {#if !showResponse}
        <!-- Emotion Selection Grid -->
        <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 max-w-3xl mx-auto">
            {#each emotions as emotion (emotion.label)}
                <button
                    on:click={emotion.action}
                    class="p-4 bg-primary-600 rounded-lg shadow text-left"
                >
                    <div class="flex items-start gap-2">
                        
                        <div>
                            <h4 class="mb-2">
                                <span class="text-2xl">{emotion.emoji}</span>
                                {emotion.label}
                            </h4>
                            <p class="text-foreground">{emotion.description}</p>
                        </div>
                    </div>
                </button>
            {/each}
        </div>
    {:else}
        <!-- Response Section -->
        <div class="max-w-2xl mx-auto bg-primary-400 rounded-lg shadow-lg p-5">
            <h4 class="font-semibold text-background mb-4">Remember Your Power</h4>
            
            {#if selectedEmotion === 'anger'}
                <p class="text-foreground mb-6">
                    Anger often comes from unmet expectations or feeling powerless. Take a deep breath. 
                    You have the power to choose your response. What would happen if you approached this 
                    situation with curiosity instead of judgment?
                </p>
            {:else if selectedEmotion === 'sadness'}
                <p class="text-foreground mb-6">
                    Sadness is a natural response to loss or disappointment. Honor this feeling, 
                    but remember: you have survived every difficult moment so far. What small act 
                    of kindness can you show yourself right now?
                </p>
            {:else if selectedEmotion === 'fear'}
                <p class="text-foreground mb-6">
                    Worry tries to control an uncertain future. But you only have this moment. 
                    What is actually within your control right now? Focus there, and let the rest go.
                </p>
            {:else if selectedEmotion === 'past'}
                <p class="text-foreground mb-6">
                    The past exists only in your mind now. You cannot change what happened, 
                    but you can change what it means to you. What wisdom did you gain that 
                    makes you stronger today?
                </p>
            {:else if selectedEmotion === 'future'}
                <p class="text-foreground mb-6">
                    The future is unwritten. Your power lies in this present moment. 
                    What one small action can you take today that your future self will thank you for?
                </p>
            {/if}
            
            <div class="pt-4 border-t">
                <p class="text-foreground italic mb-6">
                    "You are not your thoughts. You are the observer, 
                    and you have the power to choose your focus, beliefs and actions."
                </p>
            </div>
            
            <button
                on:click={resetSelection}
                class="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
                Choose Another
            </button>
        </div>
    {/if}
    
    <!-- Premium Prompt -->
    <PremiumPrompt />
</div>