# Deno Dependency Management Guide

## Overview for Node.js Developers

If you're coming from Node.js, <PERSON><PERSON> handles dependencies very differently. This guide will help you understand how to manage dependencies in Supabase Edge Functions using Deno.

## Key Differences: Node.js vs Deno

### Node.js Approach
```bash
# Install dependencies
npm install stripe @supabase/supabase-js

# Dependencies are listed in package.json
{
  "dependencies": {
    "stripe": "^14.21.0",
    "@supabase/supabase-js": "^2.0.0"
  }
}

# Import in your code
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';
```

### Deno Approach
```json
// supabase/functions/import_map.json
{
  "imports": {
    "stripe": "https://esm.sh/stripe@14.21.0",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@2"
  }
}
```

```typescript
// Import in your code (same syntax!)
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';
```

## How Deno Dependencies Work

### 1. Import Maps
Deno uses **import maps** to resolve bare module specifiers (like `'stripe'`) to actual URLs.

**Location**: `supabase/functions/import_map.json`

```json
{
  "imports": {
    "package-name": "https://cdn.example.com/package@version",
    "stripe": "https://esm.sh/stripe@14.21.0",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@2"
  }
}
```

### 2. URL-Based Imports
Deno can import modules directly from URLs:

```typescript
// Direct URL import
import Stripe from 'https://esm.sh/stripe@14.21.0';

// Using import map (preferred)
import Stripe from 'stripe';
```

### 3. Popular CDNs for Deno

#### esm.sh (Recommended)
- Converts npm packages to ES modules
- Automatic TypeScript declarations
- Example: `https://esm.sh/stripe@14.21.0`

#### deno.land/x
- Deno-native packages
- Example: `https://deno.land/x/oak@v12.6.1/mod.ts`

#### JSR (JavaScript Registry)
- Modern package registry
- Example: `jsr:@supabase/functions-js`

## Adding New Dependencies

### Step 1: Find the Package
Visit [esm.sh](https://esm.sh) and search for your package, or construct the URL:
```
https://esm.sh/[package-name]@[version]
```

### Step 2: Add to Import Map
Edit `supabase/functions/import_map.json`:

```json
{
  "imports": {
    "stripe": "https://esm.sh/stripe@14.21.0",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@2",
    "lodash": "https://esm.sh/lodash@4.17.21",
    "date-fns": "https://esm.sh/date-fns@2.30.0"
  }
}
```

### Step 3: Import in Your Code
```typescript
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';
import _ from 'lodash';
import { format } from 'date-fns';
```

## Version Management

### Exact Versions (Recommended)
```json
{
  "imports": {
    "stripe": "https://esm.sh/stripe@14.21.0"
  }
}
```

### Version Ranges
```json
{
  "imports": {
    "stripe": "https://esm.sh/stripe@^14.0.0"
  }
}
```

### Latest Version
```json
{
  "imports": {
    "stripe": "https://esm.sh/stripe"
  }
}
```

## TypeScript Support

Most packages from esm.sh include TypeScript declarations automatically:

```typescript
import Stripe from 'stripe'; // TypeScript types included!

const stripe = new Stripe(process.env.STRIPE_SECRET!, {
  apiVersion: '2022-11-15' // Full type checking
});
```

## Common Patterns

### Shared Dependencies
Create shared modules in `supabase/functions/_shared/`:

```typescript
// supabase/functions/_shared/stripeClient.ts
import Stripe from 'stripe';

export const stripe = new Stripe(Deno.env.get('STRIPE_SECRET')!, {
  apiVersion: '2022-11-15'
});

export type { Stripe };
```

```typescript
// supabase/functions/create-subscription/index.ts
import { stripe } from '../_shared/stripeClient.ts';
```

### Environment Variables
Use `Deno.env.get()` instead of `process.env`:

```typescript
// Node.js
const apiKey = process.env.STRIPE_SECRET;

// Deno
const apiKey = Deno.env.get('STRIPE_SECRET');
```

## Troubleshooting

### Common Issues

#### 1. Module Not Found
```
error: Module not found "https://esm.sh/some-package@1.0.0"
```
**Solution**: Check if the package exists on npm and esm.sh

#### 2. Type Errors
```
error: Cannot find module 'stripe' or its corresponding type declarations
```
**Solution**: Ensure the import map is correctly configured

#### 3. Version Conflicts
**Solution**: Use exact versions in import maps

### Debugging Tips

1. **Check Import Map**: Verify `import_map.json` syntax
2. **Test URLs**: Visit the esm.sh URL directly in browser
3. **Clear Cache**: Deno caches modules, use `--reload` flag if needed

## Migration Checklist

When migrating from Node.js to Deno:

- [ ] Remove `package.json` dependencies (keep devDependencies if needed)
- [ ] Create `import_map.json` in `supabase/functions/`
- [ ] Convert `process.env` to `Deno.env.get()`
- [ ] Update import statements to use import map
- [ ] Test all functionality
- [ ] Update CI/CD if applicable

## Best Practices

1. **Pin Exact Versions**: Use specific versions for production
2. **Use Import Maps**: Don't use direct URLs in code
3. **Shared Modules**: Create reusable modules in `_shared/`
4. **Environment Variables**: Use `Deno.env.get()` consistently
5. **Type Safety**: Leverage TypeScript support from esm.sh

## Example: Complete Setup

### 1. Import Map
```json
{
  "imports": {
    "stripe": "https://esm.sh/stripe@14.21.0",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@2",
    "zod": "https://esm.sh/zod@3.22.4"
  }
}
```

### 2. Shared Client
```typescript
// supabase/functions/_shared/clients.ts
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';

export const stripe = new Stripe(Deno.env.get('STRIPE_SECRET')!, {
  apiVersion: '2022-11-15'
});

export const supabase = createClient(
  Deno.env.get('SP_SUPABASE_URL')!,
  Deno.env.get('SP_SUPABASE_ANON_KEY')!
);
```

### 3. Function Implementation
```typescript
// supabase/functions/create-subscription/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { stripe } from '../_shared/clients.ts';
import { z } from 'zod';

const requestSchema = z.object({
  customerId: z.string(),
  priceId: z.string()
});

serve(async (req) => {
  try {
    const body = await req.json();
    const { customerId, priceId } = requestSchema.parse(body);
    
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }]
    });
    
    return new Response(JSON.stringify(subscription), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }
});
```

## Resources

- [Deno Manual](https://deno.land/manual)
- [esm.sh Documentation](https://esm.sh)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [Import Maps Specification](https://github.com/WICG/import-maps)