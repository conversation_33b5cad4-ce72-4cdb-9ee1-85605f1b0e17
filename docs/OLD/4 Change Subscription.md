Below is a step-by-step guide for rolling your own “change plan” and “cancel subscription” endpoints in a 100%-static Capacitor + Supabase Edge Functions setup. You’ll get full control—and avoid flaky deep-link flows—by creating two new functions plus client-side calls.

---

## 1. Prep: Your `user_subscriptions` Table

First, ensure you have a table to map users ↔ subscriptions:

```sql
create table user_subscriptions (
  user_id        uuid     references profiles(id) on delete cascade,
  subscription_id text    primary key,
  status          text,
  plan_id         text,
  updated_at      timestamptz default now()
);
```

You can upsert into this table in your existing `stripe-webhook` function on every `customer.subscription.*` event.

---

## 2. Edge Function: Swap Plans

Create a function at `supabase/functions/update-subscription/index.ts`:

```ts
import { serve } from 'std/server';
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET')!, { apiVersion: '2022-11-15' });
const supabase = createClient(
  Deno.env.get('SP_SUPABASE_URL')!,
  Deno.env.get('SP_SUPABASE_SERVICE_ROLE_KEY')!
);

serve(async (req) => {
  const { subscriptionId, newPriceId } = await req.json();

  // 1) Auth & ownership check
  const auth = req.headers.get('Authorization')!;
  const { data: session } = await supabase.auth.getUser(auth.replace('Bearer ', ''));
  if (!session.user) return new Response('Unauthorized', { status: 401 });

  const { data: subRow } = await supabase
    .from('user_subscriptions')
    .select('user_id')
    .eq('subscription_id', subscriptionId)
    .single();

  if (subRow?.user_id !== session.user.id) {
    return new Response('Subscription not found', { status: 404 });
  }

  // 2) Retrieve the subscription’s item ID
  const stripeSub = await stripe.subscriptions.retrieve(subscriptionId, {
    expand: ['items.data']
  });
  const itemId = stripeSub.items.data[0].id;

  // 3) Update subscription
  const updated = await stripe.subscriptions.update(subscriptionId, {
    items: [{ id: itemId, price: newPriceId }],
    proration_behavior: 'create_prorations'
  });

  // 4) (Optional) You can immediately update your DB too:
  await supabase.from('user_subscriptions').update({
    plan_id:  newPriceId,
    status:   updated.status,
    updated_at: new Date().toISOString()
  }).eq('subscription_id', subscriptionId);

  return new Response(JSON.stringify({
    status: updated.status,
    current_period_end: updated.current_period_end
  }), { headers: { 'Content-Type': 'application/json' } });
});
```

1. **Ownership check**: verifies the JWT user matches your table.
2. **Prorations**: uses `create_prorations` so Stripe bills or credits immediately.

Deploy with:

```bash
supabase secrets set STRIPE_SECRET="sk_live_…" 
supabase functions deploy update-subscription
```

---

## 3. Edge Function: Cancel Subscription

Create `supabase/functions/cancel-subscription/index.ts`:

```ts
import { serve } from 'std/server';
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET')!, { apiVersion: '2022-11-15' });
const supabase = createClient(
  Deno.env.get('SP_SUPABASE_URL')!,
  Deno.env.get('SP_SUPABASE_SERVICE_ROLE_KEY')!
);

serve(async (req) => {
  const { subscriptionId, atPeriodEnd = true } = await req.json();

  // 1) Auth & ownership check (same as above)
  const auth = req.headers.get('Authorization')!;
  const { data: session } = await supabase.auth.getUser(auth.replace('Bearer ', ''));
  if (!session.user) return new Response('Unauthorized', { status: 401 });

  const { data: subRow } = await supabase
    .from('user_subscriptions')
    .select('user_id')
    .eq('subscription_id', subscriptionId)
    .single();

  if (subRow?.user_id !== session.user.id) {
    return new Response('Subscription not found', { status: 404 });
  }

  // 2) Cancel immediately or at period end
  const canceled = await stripe.subscriptions.update(subscriptionId, {
    cancel_at_period_end: atPeriodEnd
  });

  // 3) Update DB
  await supabase.from('user_subscriptions').update({
    status:   canceled.status,
    updated_at: new Date().toISOString()
  }).eq('subscription_id', subscriptionId);

  return new Response(JSON.stringify({
    status: canceled.status,
    cancel_at: canceled.cancel_at
  }), { headers: { 'Content-Type': 'application/json' } });
});
```

* `atPeriodEnd=true` (default) lets them keep access until the period’s end.
* `atPeriodEnd=false` cancels immediately.

Deploy:

```bash
supabase functions deploy cancel-subscription
```

---

## 4. Client-Side in Capacitor + SvelteKit

```ts
import { supabase } from '$lib/supabase';

async function swapPlan(subscriptionId: string, newPriceId: string) {
  const { data: { session } } = await supabase.auth.getSession();
  const res = await fetch(
    'https://YOUR_REF.functions.supabase.co/update-subscription',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({ subscriptionId, newPriceId })
    }
  );
  return res.json();  // { status, current_period_end }
}

async function cancel(subscriptionId: string, atPeriodEnd = true) {
  const { data: { session } } = await supabase.auth.getSession();
  const res = await fetch(
    'https://YOUR_REF.functions.supabase.co/cancel-subscription',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({ subscriptionId, atPeriodEnd })
    }
  );
  return res.json();  // { status, cancel_at }
}
```

1. Call `swapPlan(...)` when the user selects a new tier.
2. Call `cancel(...)` when they hit “Cancel subscription.”
3. Update your UI based on the returned `status` and dates.

---

## 5. Keep Everything in Sync

Your existing `stripe-webhook` function will catch all subscription updates and cancellations, upserting into `user_subscriptions`. That way, even if you miss a client-side update, your DB reflects the true Stripe state.

---

#### Bottom Line

* **You control exactly how and when** proration is applied, when cancellations take effect, and how you inform users.
* **Your DB stays authoritative** via your webhook.
* **Your Capacitor app remains purely static**—all business logic lives in secure Edge Functions.
