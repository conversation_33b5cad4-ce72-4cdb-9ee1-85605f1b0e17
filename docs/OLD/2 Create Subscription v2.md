Below is the end-to-end plan—assuming a **purely static** SSG build and hosting **all** your runtime logic on Supabase Edge Functions. A complete `.env.example` follows at the end, with **every** variable explained.

---

## 1. Prerequisites

* **Supabase project** with

  * API settings: URL & anon key
  * Service-role key (for Edge Functions)
* **Stripe** account with

  * Secret key
  * Publishable key
  * Webhook signing secret
* **Supabase CLI** installed & linked:

  ```bash
  npm install -g supabase
  supabase login
  supabase link --project-ref YOUR_PROJECT_REF
  ```

---

## 2. Edge Function: `create-subscription`

1. **Generate** the function:

   ```bash
   supabase functions new create-subscription
   ```

2. **Code** (`supabase/functions/create-subscription/index.ts`):

   ```ts
   // Setup type definitions for built-in Supabase Runtime APIs
   import "jsr:@supabase/functions-js/edge-runtime.d.ts"
   
   import Stripe from 'stripe';
   import { createClient } from '@supabase/supabase-js';

   //–– Env vars (see .env.example below)
   const stripe = new Stripe(Deno.env.get('STRIPE_SECRET')!, { apiVersion: '2022-11-15' });
   const supabase = createClient(
     Deno.env.get('SP_SUPABASE_URL')!,
     Deno.env.get('SP_SUPABASE_SERVICE_ROLE_KEY')!
   );

   Deno.serve(async (req) => {
     const { priceId } = await req.json();
     const token = req.headers.get('Authorization')?.replace('Bearer ', '');
     if (!token) return new Response('Unauthorized', { status: 401 });

     // 1. Identify user from JWT
     const { data: { user }, error: authErr } = await supabase.auth.getUser(token);
     if (authErr || !user) return new Response('Invalid user', { status: 401 });

     // 2. Ensure Stripe customer
     let { data: profile } = await supabase
       .from('profiles').select('stripe_customer_id,email').eq('id', user.id).single();
     let customerId = profile?.stripe_customer_id;
     if (!customerId) {
       const cust = await stripe.customers.create({ email: user.email! });
       customerId = cust.id;
       await supabase
         .from('profiles')
         .update({ stripe_customer_id: customerId })
         .eq('id', user.id);
     }

     // 3. Create incomplete subscription + get client_secret
     const subscription = await stripe.subscriptions.create({
       customer: customerId,
       items: [{ price: priceId }],
       payment_behavior: 'default_incomplete',
       expand: ['latest_invoice.payment_intent']
     });

     return new Response(JSON.stringify({
       subscriptionId: subscription.id,
       clientSecret: subscription.latest_invoice.payment_intent.client_secret
     }), { headers: { 'Content-Type': 'application/json' } });
   })
   ```

3. **Configure secrets** and deploy:

   ```bash
   supabase secrets set STRIPE_SECRET="sk_live_…"  
   supabase secrets set SP_SUPABASE_SERVICE_ROLE_KEY="YOUR_SERVICE_ROLE_KEY"  
   supabase functions deploy create-subscription
   ```

---

## 3. Client-side SvelteKit + Capacitor

Install and configure your clients:

```bash
npm install @supabase/supabase-js @stripe/stripe-js
```

```ts
// src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js';
export const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);
```

```ts
// src/routes/+page.svelte (or wherever your flow lives)
<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { loadStripe } from '@stripe/stripe-js';
  import { onMount } from 'svelte';

  let elements: stripe.Elements;
  let clientSecret: string;
  const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

  async function startSubscription(priceId: string) {
    // 1. Call Edge Function
    const { data: session } = await supabase.auth.getSession();
    const res = await fetch(
      `https://${import.meta.env.VITE_SUPABASE_PROJECT_REF}.functions.supabase.co/create-subscription`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token}`
        },
        body: JSON.stringify({ priceId })
      }
    );
    const { clientSecret: cs } = await res.json();
    clientSecret = cs;

    // 2. Mount Payment Element
    elements = stripe.elements();
    const paymentEl = elements.create('payment');
    paymentEl.mount('#payment-element');
  }

  async function confirm() {
    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {}
    });
    if (error) {
      console.error(error);
      return;
    }
    // 3. Optionally record in your table (webhook will also sync renewals)
    const { data: { user } } = await supabase.auth.getUser();
    await supabase.from('user_subscriptions').upsert({
      user_id: user!.id,
      subscription_id: paymentIntent!.id,
      status: paymentIntent!.status
    });
  }
</script>

<div id="payment-element"></div>
<button on:click={() => confirm()}>Subscribe</button>
```

---

## 4. Edge Function: `stripe-webhook`

1. **New function**:

   ```bash
   supabase functions new stripe-webhook
   ```

2. **Code** (`supabase/functions/stripe-webhook/index.ts`):

   ```ts
   // Setup type definitions for built-in Supabase Runtime APIs
   import "jsr:@supabase/functions-js/edge-runtime.d.ts"
   
   import Stripe from 'stripe';
   import { createClient } from '@supabase/supabase-js';

   const stripe = new Stripe(Deno.env.get('STRIPE_SECRET')!, { apiVersion: '2022-11-15' });
   const supabase = createClient(
     Deno.env.get('SP_SUPABASE_URL')!,
     Deno.env.get('SP_SUPABASE_SERVICE_ROLE_KEY')!
   );
   const endpointSecret = Deno.env.get('SP_STRIPE_WEBHOOK_SECRET')!;

   Deno.serve(async (req) => {
     const sig = req.headers.get('stripe-signature')!;
     const body = await req.text();
     let evt: Stripe.Event;
     try {
       evt = stripe.webhooks.constructEvent(body, sig, endpointSecret);
     } catch {
       return new Response('Invalid signature', { status: 400 });
     }

     if (evt.type.startsWith('customer.subscription')) {
       const sub = evt.data.object as Stripe.Subscription;
       // Lookup your user
       const { data: profile } = await supabase
         .from('profiles')
         .select('id')
         .eq('stripe_customer_id', sub.customer)
         .single();
       if (profile) {
         await supabase.from('user_subscriptions').upsert({
           user_id: profile.id,
           subscription_id: sub.id,
           status: sub.status
         });
       }
     }
     // handle invoice.payment_succeeded/failed similarly…

     return new Response(null, { status: 200 });
   })
   ```

3. **Secrets & deploy**:

   ```bash
   supabase secrets set SP_STRIPE_WEBHOOK_SECRET="whsec_…"  
   supabase functions deploy stripe-webhook
   ```

4. **Register** in Stripe Dashboard → Webhooks →

   ```
   https://YOUR_PROJECT_REF.functions.supabase.co/stripe-webhook
   ```

---

## .env.example

```dotenv
# ────────────── Supabase (client & server) ──────────────

# Your Supabase project URL
# Found in Dashboard → Settings → API → Project URL
VITE_SUPABASE_URL=https://xyzabc123.supabase.co

# Public anon key for client-side auth
# Found in Dashboard → Settings → API → Project API keys
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIs...

# Service-role key for server-side operations (Edge Functions)
# Found in Dashboard → Settings → API → Project API keys
# Keep this secret; used by functions to read/write any table
SP_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIs...

# Your Supabase project ref (for function URLs)
# Shown in supabase link output, also in Dashboard URL
# e.g. "xyzabc123"
VITE_SUPABASE_PROJECT_REF=xyzabc123

# ─────────────── Stripe (secret & publishable) ───────────────

# Secret API key (server-side)
# Stripe Dashboard → Developers → API Keys → Secret key
STRIPE_SECRET=sk_live_51H...

# Publishable key for Stripe.js (client-side)
# Stripe Dashboard → Developers → API Keys → Publishable key
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_51H...

# Webhook signing secret
# Stripe Dashboard → Developers → Webhooks → click your endpoint → Signing secret
SP_STRIPE_WEBHOOK_SECRET=whsec_...

```

**All set.** Your Capacitor app remains purely static, with **no** `+server.ts` in the bundle—everything runs securely on Supabase Edge Functions.
