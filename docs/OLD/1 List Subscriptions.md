Here’s a minimal end-to-end for listing your three recurring prices via a Supabase Edge Function and consuming them in your Capacitor SvelteKit client:

---

## 1. Create a `list-subscriptions` Edge Function

1. **Generate the function**

   ```bash
   supabase functions new list-subscriptions
   ```

2. **Implement it** in `supabase/functions/list-subscriptions/index.ts`:

   ```ts
  import Stripe from 'stripe'
  import { createClient } from '@supabase/supabase-js'

  const stripe = new Stripe(Deno.env.get('STRIPE_SECRET')!, {
    apiVersion: '2022-11-15',
  })
  const supabase = createClient(
    Deno.env.get('SP_SUPABASE_URL')!,
    Deno.env.get('SP_SUPABASE_SERVICE_ROLE_KEY')!
  )

  Deno.serve(async (req) => {
    // 1. auth
    const auth = req.headers.get('Authorization')?.replace('Bearer ', '')
    if (!auth) return new Response('Unauthorized', { status: 401 })
    const { data: { user } } = await supabase.auth.getUser(auth)
    if (!user) return new Response('Unauthorized', { status: 401 })

    // 2. list recurring prices
    const prices = await stripe.prices.list({
      product: Deno.env.get('STRIPE_PRODUCT_ID'),
      active: true,
      type: 'recurring',
      limit: 10,
    })

    // 3. map to minimal shape
    const plans = prices.data.map(p => ({
      priceId:  p.id,
      nickname: p.nickname,
      interval: p.recurring.interval,
      interval_count: p.recurring.interval_count,
      amount:   p.unit_amount,
      currency: p.currency,
    }))

    // 4. return JSON
    return new Response(JSON.stringify({ plans }), {
      headers: { 'Content-Type': 'application/json' },
    })
  })
   ```

3. **Set your product ID secret**

   ```bash
   supabase secrets set STRIPE_PRODUCT_ID="prod_XXXXXXXX"
   ```

4. **Deploy**

   ```bash
   supabase functions deploy list-subscriptions
   ```

---

## 2. Fetch and render on the client

```ts
<script lang="ts">
  import { onMount } from 'svelte';
  import { supabase } from '$lib/supabase';

  type Plan = {
    priceId: string;
    nickname: string;
    interval: string;
    amount: number;
    currency: string;
  };

  let plans: Plan[] = [];
  let error: string | null = null;

  onMount(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      error = 'Not signed in';
      return;
    }

    const res = await fetch(
      'https://YOUR_PROJECT_REF.functions.supabase.co/list-subscriptions',
      {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      }
    );

    if (!res.ok) {
      error = `Error ${res.status}`;
      return;
    }
    const json = await res.json();
    plans = json.plans;
  });
</script>

{#if error}
  <p class="text-red-600">{error}</p>
{:else}
  <ul>
    {#each plans as plan}
      <li class="p-2 border-b">
        <strong>{plan.nickname}</strong>
        — {plan.amount / 100} {plan.currency.toUpperCase()} / {plan.interval}
        <button on:click={() => selectPlan(plan.priceId)}>
          Subscribe
        </button>
      </li>
    {/each}
  </ul>
{/if}
```

1. **List items**: show nickname, formatted price, and billing interval.
2. **Subscribe button**: pass `plan.priceId` into your existing `create-subscription` flow.

---

## 3. Why this works

* **Edge-only** – no Node runtime in your SSG.
* **Secure** – JWT from `supabase.auth` gates both list and create calls.
* **Dynamic** – you’ll automatically surface any new prices you add in Stripe.

That’s it: now your static Capacitor/SvelteKit app can discover and render all three subscription options at runtime.
