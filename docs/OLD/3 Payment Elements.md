Here’s a straight-forward, end-to-end recipe for embedding Stripe’s Payment Element in your static Capacitor + SvelteKit app against your Supabase Edge Functions.

---

## 1. Install & load Stripe.js

```bash
npm install @stripe/stripe-js
```

In your root `app.html` (or equivalent), include:

```html
<script async src="https://js.stripe.com/v3/"></script>
```

This ensures the global `Stripe` object is available if you ever need it directly.

---

## 2. Create a `PaymentForm.svelte` component

```svelte
<script lang="ts">
  import { onMount } from 'svelte';
  import { loadStripe, Stripe, StripeElements, PaymentElement } from '@stripe/stripe-js';
  import { supabase } from '$lib/supabase';

  let stripe: Stripe | null = null;
  let elements: StripeElements | null = null;
  let paymentElement: PaymentElement;
  let clientSecret: string;
  let loading = true;
  let errorMsg: string | null = null;

  onMount(async () => {
    // 1. fetch client secret from your Edge Function
    const { data: { session } } = await supabase.auth.getSession();
    const token = session.access_token;
    const resp = await fetch(
      'https://YOUR_PROJECT_REF.functions.supabase.co/create-subscription',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ priceId: 'price_…' })
      }
    );
    const { clientSecret: cs } = await resp.json();
    clientSecret = cs;

    // 2. initialize Stripe.js + Elements
    stripe = await loadStripe('pk_live_…');
    elements = stripe!.elements({ clientSecret });

    // 3. mount the Payment Element
    paymentElement = elements.create('payment');
    paymentElement.mount('#payment-element');

    loading = false;
  });

  async function handleSubmit() {
    loading = true;
    errorMsg = null;

    const { error } = await stripe!.confirmPayment({
      elements,
      confirmParams: {
        // no web return_url needed in Capacitor; confirmPayment handles it inline
      },
      redirect: 'if_required'
    });

    if (error) {
      errorMsg = error.message;
      loading = false;
    } else {
      // success: webhook + optional upsert will sync status
      // you can show a “Thank you” screen here
    }
  }
</script>

{#if loading}
  <p>Loading payment form…</p>
{:else}
  <form on:submit|preventDefault={handleSubmit}>
    <div id="payment-element" class="border p-4 rounded"></div>
    {#if errorMsg}
      <p class="text-red-600">{errorMsg}</p>
    {/if}
    <button type="submit" class="mt-4 btn" disabled={loading}>
      {loading ? 'Processing…' : 'Subscribe'}
    </button>
  </form>
{/if}

<style>
  /* your Tailwind or custom styling here */
</style>
```

### Key points

1. **Fetch `clientSecret`** from your “create-subscription” Edge Function.
2. **Load Stripe.js** via `loadStripe('pk_live…')`.
3. **Initialize Elements** with `{ clientSecret }`.
4. **Create & mount** the Payment Element in a container `<div id="payment-element">`.
5. **Call `confirmPayment`** with `redirect: 'if_required'` so it stays “in-app.”

---

## 3. Error handling & UX

* Show a spinner or disabled button while `confirmPayment` is in progress.
* Surface `error.message` from Stripe to the user.
* On success, navigate to a “Thank you” or “Subscriptions” view—your webhook will have updated your `user_subscriptions` table, so you can re-fetch and display current status.

---

## 4. Styling & branding

* **Payment Element** picks up your Stripe Dashboard colors by default.
* Override via CSS variables if you need a tighter brand match; see Stripe’s \[theming docs].

---

With this in place, your Capacitor app remains 100 % static:

* **All sensitive logic** lives in Supabase Edge Functions.
* **Stripe Elements** handles card collection inline.
* **Webhooks** keep your database in sync.
