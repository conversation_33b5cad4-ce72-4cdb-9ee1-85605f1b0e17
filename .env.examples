# Stripe configuration
SP_STRIPE_PUBLIC_KEY=pk......
SP_STRIPE_SECRET_KEY=sk_test_.......
SP_STRIPE_PRODUCT_ID=prod_.......
# After creating the stripe webhook endpoint, click on it Reveal and copy the "Signing secret" (starts with `whsec_`) (not the destination id)
SP_STRIPE_WEBHOOK_SECRET=...

# Supabase configuration

# Supabase local docker example http://host.docker.internal:54321 instead of localhost or 127...
# Production use https://your-project.supabase.co
SP_SUPABASE_URL=http://host.docker.internal:54321

# Get these values from "npx supabase status" if doing local docker testing
SP_SUPABASE_ANON_KEY=......

SP_SUPABASE_SERVICE_ROLE_KEY=......

SP_SUPABASE_JWT_SECRET=....
