const { config } = require('dotenv');
const { resolve } = require('path');
const request = require('supertest');
const { createClient } = require('@supabase/supabase-js');

// Depends on .env with:
// SP_SUPABASE_URL=....
// SP_SUPABASE_ANON_KEY=....

// Load environment variables
config({ path: resolve(__dirname, '.env') });

// Initialize Supabase client
const supabaseUrl = process.env.SP_SUPABASE_URL;
const supabaseAnonKey = process.env.SP_SUPABASE_ANON_KEY || process.env.SUPABASE_PUBLIC_KEY;

// Validate environment variables
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('\n❌ Missing required environment variables!');
  console.error('   Please create a test/.env file with:');
  console.error('   - SP_SUPABASE_URL (e.g., http://127.0.0.1:54321 for local)');
  console.error('   - SP_SUPABASE_ANON_KEY');
  console.error('\n   See test/.env.example for reference');
  process.exit(1);
}

// Check if we're using local development
const isLocalDev = supabaseUrl && supabaseUrl.includes('127.0.0.1:54321');

// Create Supabase client with appropriate configuration
const supabaseOptions = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  }
};

// For local development, ensure we're using the local instance
if (isLocalDev) {
  console.log('🔧 Configuring Supabase client for local development...');
  console.log('   Auth requests will go to: http://127.0.0.1:54321/auth/v1/');
} else {
  console.log('⚠️  WARNING: Using production Supabase instance!');
  console.log('   Auth requests will go to production Supabase');
  console.log('   To use local development, set SP_SUPABASE_URL=http://127.0.0.1:54321');
}

const supabase = createClient(supabaseUrl, supabaseAnonKey, supabaseOptions);

// Test configuration
const FUNCTIONS_URL = isLocalDev ? 'http://127.0.0.1:54321/functions/v1' : `${supabaseUrl}/functions/v1`;
const TEST_EMAIL = `test-${Date.now()}@example.com`;
const TEST_PASSWORD = 'teMVMV23sayKMord123';

// Stripe test price ID (you'll need to replace this with your actual test price ID)
const TEST_PRICE_ID = process.env.STRIPE_TEST_PRICE_ID || 'price_1RhO2CD7PiFPw0TZiUeAgmVI'; // Replace with your Stripe test price ID

// Debug mode - set to true to see detailed logs
const DEBUG = process.env.DEBUG === 'true' || process.argv.includes('--debug');

// Helper function to log debug information
function debugLog(message, data) {
  if (DEBUG) {
    console.log(`\n🔍 DEBUG: ${message}`);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  }
}

// Helper function to make requests with better error handling
async function makeRequest(method, path, options = {}) {
  const { headers = {}, body, expectedStatus } = options;
  
  debugLog(`Making ${method} request to ${path}`, { headers, body });
  
  let req = request(FUNCTIONS_URL)[method.toLowerCase()](path);
  
  // Add headers
  Object.entries(headers).forEach(([key, value]) => {
    req = req.set(key, value);
  });
  
  // Add body if present
  if (body) {
    req = req.send(body);
  }
  
  const response = await req;
  
  // Log response details for debugging
  if (response.status !== expectedStatus && response.status !== 200) {
    console.error(`\n❌ Request failed: ${method} ${path}`);
    console.error(`   Expected status: ${expectedStatus || 200}, Got: ${response.status}`);
    console.error(`   Response headers:`, response.headers);
    console.error(`   Response body:`, response.body);
    console.error(`   Response text:`, response.text);
    
    // If it's a 503, it might be a service unavailable error
    if (response.status === 503) {
      console.error('\n⚠️  503 Service Unavailable - Possible causes:');
      console.error('   - Edge function is not running');
      console.error('   - Database connection issues');
      console.error('   - Stripe API connection issues');
      console.error('   - Check supabase logs: npx supabase functions serve');
    }
  }
  
  debugLog(`Response received`, { 
    status: response.status, 
    body: response.body,
    headers: response.headers 
  });
  
  return response;
}

describe('Create Subscription API', () => {
  let authToken;
  let userId;

  beforeAll(async () => {
    try {
      console.log('\n🔧 Setting up test environment...');
      console.log(`   Supabase URL: ${supabaseUrl}`);
      console.log(`   Environment: ${isLocalDev ? 'Local Development' : 'Production'}`);
      console.log(`   Functions URL: ${FUNCTIONS_URL}`);
      console.log(`   Test email: ${TEST_EMAIL}`);
      console.log(`   Test price ID: ${TEST_PRICE_ID}`);
      
      // 1. Sign up a new user
      debugLog('Signing up new user');
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: TEST_EMAIL,
        password: TEST_PASSWORD,
      });

      if (signUpError) {
        throw new Error(`Failed to sign up: ${signUpError.message}`);
      }

      // 2. Sign in to get the session
      debugLog('Signing in user');
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: TEST_EMAIL,
        password: TEST_PASSWORD,
      });

      if (signInError) {
        throw new Error(`Failed to sign in: ${signInError.message}`);
      }

      authToken = signInData.session.access_token;
      userId = signInData.user.id;
      
      console.log(`✅ Test user created successfully (ID: ${userId})`);
      debugLog('Auth token obtained', { tokenLength: authToken.length });


      // Output the Stripe client JWT immediately when received
        console.log('\n🔑 *********************************************');
        console.log('\n🔑 Stripe Client JWT (clientSecret) token received:');
        console.log(`   ${authToken}`);

    } catch (error) {
      console.error('\n❌ Test setup failed:', error.message);
      console.error('   Full error:', error);
      process.exit(1); // Exit immediately to prevent cascade failures
    }
  });

  afterAll(async () => {
    // Clean up: sign out
    await supabase.auth.signOut();
    console.log('\n🧹 Test cleanup completed');
  });

  describe('POST /create-subscription', () => {
    it('should create a subscription with valid priceId and authentication', async () => {
      console.log('\n📝 Test: Creating subscription with valid priceId');
      
      const response = await makeRequest('POST', '/create-subscription', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: { priceId: TEST_PRICE_ID },
        expectedStatus: 200
      });

      expect(response.status).toBe(200);
      // expect(response.body).toHaveProperty('subscriptionId');
      // expect(response.body).toHaveProperty('clientSecret');
      // expect(typeof response.body.subscriptionId).toBe('string');
      // expect(typeof response.body.clientSecret).toBe('string');
      
      console.log('\n✅ Subscription created successfully');
      debugLog('Subscription details', {
        subscriptionId: response.body.subscriptionId,
        clientSecretLength: response.body.clientSecret?.length
      });
    });

  //   it('should return 401 without authentication', async () => {
  //     console.log('\n📝 Test: Attempting without authentication');
      
  //     const response = await makeRequest('POST', '/create-subscription', {
  //       headers: {
  //         'Content-Type': 'application/json'
  //       },
  //       body: { priceId: TEST_PRICE_ID },
  //       expectedStatus: 401
  //     });

  //     expect(response.status).toBe(401);
  //     expect(response.body).toHaveProperty('error');
  //     console.log('✅ Correctly rejected unauthenticated request');
  //   });

  //   it('should return 400 without priceId', async () => {
  //     console.log('\n📝 Test: Attempting without priceId');
      
  //     const response = await makeRequest('POST', '/create-subscription', {
  //       headers: {
  //         'Authorization': `Bearer ${authToken}`,
  //         'Content-Type': 'application/json'
  //       },
  //       body: {},
  //       expectedStatus: 400
  //     });

  //     expect(response.status).toBe(400);
  //     expect(response.body).toHaveProperty('error', 'Price ID is required');
  //     console.log('✅ Correctly rejected request without priceId');
  //   });

  //   it('should return 400 with empty priceId', async () => {
  //     console.log('\n📝 Test: Attempting with empty priceId');
      
  //     const response = await makeRequest('POST', '/create-subscription', {
  //       headers: {
  //         'Authorization': `Bearer ${authToken}`,
  //         'Content-Type': 'application/json'
  //       },
  //       body: { priceId: '' },
  //       expectedStatus: 400
  //     });

  //     expect(response.status).toBe(400);
  //     expect(response.body).toHaveProperty('error', 'Price ID is required');
  //     console.log('✅ Correctly rejected request with empty priceId');
  //   });

  //   it('should handle invalid priceId gracefully', async () => {
  //     console.log('\n📝 Test: Attempting with invalid priceId');
      
  //     const response = await makeRequest('POST', '/create-subscription', {
  //       headers: {
  //         'Authorization': `Bearer ${authToken}`,
  //         'Content-Type': 'application/json'
  //       },
  //       body: { priceId: 'invalid_price_id' },
  //       expectedStatus: 500 // Expecting 500 for Stripe API error
  //     });

  //     // This will likely return 500 due to Stripe API error
  //     expect(response.status).toBeGreaterThanOrEqual(400);
  //     expect(response.body).toHaveProperty('error');
  //     console.log('✅ Correctly handled invalid priceId');
  //   });

  //   it('should handle CORS preflight requests', async () => {
  //     console.log('\n📝 Test: CORS preflight request');
      
  //     const response = await makeRequest('OPTIONS', '/create-subscription', {
  //       headers: {
  //         'Origin': 'http://localhost:3000',
  //         'Access-Control-Request-Method': 'POST',
  //         'Access-Control-Request-Headers': 'authorization,content-type'
  //       },
  //       expectedStatus: 200
  //     });

  //     expect(response.status).toBe(200);
  //     expect(response.headers).toHaveProperty('access-control-allow-origin');
  //     expect(response.headers).toHaveProperty('access-control-allow-headers');
  //     console.log('✅ CORS preflight handled correctly');
  //   });
  // });

  // describe('Stripe customer creation', () => {
  //   it('should create a new Stripe customer if one does not exist', async () => {
  //     console.log('\n📝 Test: Verifying Stripe customer creation');
      
  //     // This is tested implicitly in the first test, but we can verify by checking the database
  //     const { data: profile, error } = await supabase
  //       .from('profiles')
  //       .select('stripe_customer_id')
  //       .eq('id', userId)
  //       .single();

  //     if (error) {
  //       console.error('Failed to fetch profile:', error);
  //     }

  //     expect(profile).toBeTruthy();
  //     expect(profile.stripe_customer_id).toBeTruthy();
  //     expect(typeof profile.stripe_customer_id).toBe('string');
  //     expect(profile.stripe_customer_id).toMatch(/^cus_/); // Stripe customer IDs start with 'cus_'
      
  //     console.log(`✅ Stripe customer created: ${profile.stripe_customer_id}`);
  //   });

  //   it('should reuse existing Stripe customer on subsequent calls', async () => {
  //     console.log('\n📝 Test: Verifying Stripe customer reuse');
      
  //     // Get the current stripe_customer_id
  //     const { data: profileBefore } = await supabase
  //       .from('profiles')
  //       .select('stripe_customer_id')
  //       .eq('id', userId)
  //       .single();

  //     const customerIdBefore = profileBefore.stripe_customer_id;
  //     console.log(`   Existing customer ID: ${customerIdBefore}`);

  //     // Create another subscription
  //     const response = await makeRequest('POST', '/create-subscription', {
  //       headers: {
  //         'Authorization': `Bearer ${authToken}`,
  //         'Content-Type': 'application/json'
  //       },
  //       body: { priceId: TEST_PRICE_ID },
  //       expectedStatus: 200
  //     });

  //     expect(response.status).toBe(200);

  //     // Check that the stripe_customer_id hasn't changed
  //     const { data: profileAfter } = await supabase
  //       .from('profiles')
  //       .select('stripe_customer_id')
  //       .eq('id', userId)
  //       .single();

  //     expect(profileAfter.stripe_customer_id).toBe(customerIdBefore);
  //     console.log('✅ Stripe customer correctly reused');
  //   });

  });
});
