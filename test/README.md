# Supabase Edge Functions Tests

This directory contains integration tests for the Supabase Edge Functions.

## Setup

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Fill in your Supabase project details in `.env`:
   - `SP_SUPABASE_URL`: Your Supabase project URL
     - For local development: `http://127.0.0.1:54321`
     - For production testing: `https://your-project.supabase.co`
   - `SP_SUPABASE_ANON_KEY`: Your Supabase anon/public key
   - `STRIPE_TEST_PRICE_ID`: (Optional) Your Stripe test price ID

3. Install dependencies:
   ```bash
   npm install
   ```

## Running Tests

### Local Development Testing (Recommended)

1. Make sure your Supabase local development environment is running:
   ```bash
   npx supabase start
   npx supabase functions serve
   ```

2. Ensure your `.env` file uses local URLs:
   ```
   SP_SUPABASE_URL=http://127.0.0.1:54321
   SP_SUPABASE_ANON_KEY=your-local-anon-key
   ```

3. Run the tests:
   ```bash
   npm test
   ```

### Production Testing

⚠️ **Warning**: This will create test data in your production database!

1. Update your `.env` file with production URLs:
   ```
   SP_SUPABASE_URL=https://your-project.supabase.co
   SP_SUPABASE_ANON_KEY=your-production-anon-key
   ```

2. Run the tests:
   ```bash
   npm test
   ```

The tests will automatically detect whether you're testing against local or production based on the URL.

### Debug Mode

To see detailed logs and debugging information, run tests in debug mode:

```bash
# Using environment variable
DEBUG=true npm test

# Or using command line flag
npm test -- --debug
```

Debug mode will show:
- Detailed request/response information
- Full error messages and response bodies
- Helpful error diagnostics for common issues (503 errors, etc.)
- Step-by-step test execution logs

## Test Structure

- `create-subscription.test.js`: Tests for the create-subscription edge function
  - Authentication validation
  - Price ID validation
  - Stripe customer creation
  - Subscription creation with payment intent

## What Happens When Tests Fail

The improved test suite now automatically shows detailed error information when tests fail. For example, when you get a 503 instead of 200, you'll see:

```
❌ Request failed: POST /create-subscription
   Expected status: 200, Got: 503
   Response headers: { ... }
   Response body: { "error": "actual error message here" }
   Response text: (raw response if not JSON)

⚠️  503 Service Unavailable - Possible causes:
   - Edge function is not running
   - Database connection issues
   - Stripe API connection issues
   - Check supabase logs: npx supabase functions serve
```

This makes it easy to see exactly why the test failed without needing to add console.log statements or modify the edge function.

## Debugging Common Issues

### 503 Service Unavailable
- Edge function is not running - check `npx supabase functions serve`
- Database connection issues - check Supabase logs
- Stripe API connection issues - verify API keys
- Environment variables not loaded - check .env file

### 500 Internal Server Error
- Check the response body for detailed error messages
- Verify Stripe price ID is valid
- Check edge function logs for stack traces

### 401 Unauthorized
- Verify authentication token is being sent
- Check if user session is valid
- Ensure Supabase anon key is correct

## Local vs Production Testing

The test suite automatically detects your environment based on `SP_SUPABASE_URL`:

- **Local Development** (`http://127.0.0.1:54321`):
  - All auth requests go to local Supabase auth service
  - Functions are called at local endpoints
  - Ideal for development and debugging
  - No impact on production data

- **Production Testing** (`https://your-project.supabase.co`):
  - Auth requests go to production Supabase
  - Functions are called at production endpoints
  - Creates real test data in production database
  - Use with caution!

## Notes

- Tests use a test Stripe price ID that needs to be updated with your actual test price
- Each test run creates a new test user to ensure isolation
- The tests verify both the API responses and database state changes
- All error responses are automatically logged with full details when tests fail
- Make sure you're using Stripe test keys, not production keys
- When testing locally, ensure both `supabase start` and `supabase functions serve` are running
