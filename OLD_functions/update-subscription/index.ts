// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"

import { stripe } from '../_shared/stripeClient.ts'
import { supabaseAdmin } from '../_shared/supabaseAdmin.ts'
import { authenticateUser } from '../_shared/auth.ts'
import { createCorsResponse, createErrorResponse, CORS_HEADERS } from '../_shared/cors.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: CORS_HEADERS })
  }

  try {
    const { subscriptionId, newPriceId } = await req.json()
    
    if (!subscriptionId || !newPriceId) {
      return createErrorResponse('Subscription ID and new price ID are required', 400)
    }

    // 1. Authenticate user
    const { user, error: authError } = await authenticateUser(req)
    if (authError || !user) {
      return createErrorResponse(authError || 'Authentication failed', 401)
    }

    // 2. Verify ownership of subscription
    const { data: subRow } = await supabaseAdmin
      .from('user_subscriptions')
      .select('user_id')
      .eq('subscription_id', subscriptionId)
      .single()

    if (subRow?.user_id !== user.id) {
      return createErrorResponse('Subscription not found', 404)
    }

    // 3. Retrieve the subscription's item ID from Stripe
    const stripeSub = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['items.data']
    })
    
    if (!stripeSub.items.data.length) {
      return createErrorResponse('No subscription items found', 400)
    }
    
    const itemId = stripeSub.items.data[0].id

    // 4. Update subscription with new price
    const updated = await stripe.subscriptions.update(subscriptionId, {
      items: [{ id: itemId, price: newPriceId }],
      proration_behavior: 'create_prorations'
    })

    // 5. Update local database
    await supabaseAdmin
      .from('user_subscriptions')
      .update({
        plan_id: newPriceId,
        status: updated.status,
        updated_at: new Date().toISOString()
      })
      .eq('subscription_id', subscriptionId)

    return createCorsResponse({
      status: updated.status,
      current_period_end: updated.current_period_end
    })

  } catch (error) {
    console.error('Error updating subscription:', error)
    return createErrorResponse('Internal server error', 500)
  }
})