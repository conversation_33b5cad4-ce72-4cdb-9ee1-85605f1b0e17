// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"

import { stripe } from '../_shared/stripeClient.ts'
import { supabaseAdmin } from '../_shared/supabaseAdmin.ts'
import { authenticateUser } from '../_shared/auth.ts'
import { createCorsResponse, createErrorResponse, CORS_HEADERS } from '../_shared/cors.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: CORS_HEADERS })
  }

  try {
    const { subscriptionId, atPeriodEnd = true } = await req.json()
    
    if (!subscriptionId) {
      return createErrorResponse('Subscription ID is required', 400)
    }

    // 1. Authenticate user
    const { user, error: authError } = await authenticateUser(req)
    if (authError || !user) {
      return createErrorResponse(authError || 'Authentication failed', 401)
    }

    // 2. Verify ownership of subscription
    const { data: subRow } = await supabaseAdmin
      .from('user_subscriptions')
      .select('user_id')
      .eq('subscription_id', subscriptionId)
      .single()

    if (subRow?.user_id !== user.id) {
      return createErrorResponse('Subscription not found', 404)
    }

    // 3. Cancel subscription (immediately or at period end)
    const canceled = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: atPeriodEnd
    })

    // 4. Update local database
    await supabaseAdmin
      .from('user_subscriptions')
      .update({
        status: canceled.status,
        updated_at: new Date().toISOString()
      })
      .eq('subscription_id', subscriptionId)

    return createCorsResponse({
      status: canceled.status,
      cancel_at: canceled.cancel_at,
      cancel_at_period_end: canceled.cancel_at_period_end
    })

  } catch (error) {
    console.error('Error canceling subscription:', error)
    return createErrorResponse('Internal server error', 500)
  }
})