# Create Subscription API Documentation

This endpoint creates a new Stripe subscription for the authenticated user.

## Endpoint

- **Method**: POST
- **Local URL**: `http://localhost:54321/functions/v1/create-subscription`
- **Production URL**: `https://YOUR_SUPABASE_URL/functions/v1/create-subscription`

## Prerequisites

Before using this endpoint, ensure you have:
- Your Supabase project URL (replace `YOUR_SUPABASE_URL`)
- Your Supabase anon key (replace `YOUR_ANON_KEY`)
- A valid JWT token from Supabase Auth (replace `YOUR_JWT_TOKEN`)
- A valid Stripe price ID (replace `price_1234567890`)

## Request

### Headers

| Header | Value | Required | Environment |
|--------|-------|----------|-------------|
| Authorization | Bearer YOUR_JWT_TOKEN | Yes | All |
| Content-Type | application/json | Yes | All |
| apikey | YOUR_ANON_KEY | Yes | Production only |

### Body

```json
{
  "priceId": "price_1234567890"
}
```

## Response

### Success Response (200)

```json
{
  "subscriptionId": "sub_1234567890",
  "clientSecret": "pi_1234567890_secret_abcdef"
}
```

### Error Responses

| Status | Error | Description |
|--------|-------|-------------|
| 400 | `{"error": "Price ID is required"}` | Missing priceId in request body |
| 401 | `{"error": "Authentication failed"}` | Invalid or missing authentication token |
| 500 | `{"error": "Internal server error"}` | Server-side error |

## Examples

### cURL

#### Local Development
```bash
curl -X POST http://localhost:54321/functions/v1/create-subscription \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"priceId": "price_1234567890"}'
```

#### Production
```bash
curl -X POST https://YOUR_SUPABASE_URL/functions/v1/create-subscription \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "apikey: YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"priceId": "price_1234567890"}'
```

### Postman

1. Create a new POST request
2. URL: 
   - Local: `http://localhost:54321/functions/v1/create-subscription`
   - Production: `https://YOUR_SUPABASE_URL/functions/v1/create-subscription`

3. Headers tab:
   - Authorization: `Bearer USER_JWT_TOKEN` from the end users supabase login
   - Content-Type: `application/json`
   - apikey: `YOUR_ANON_KEY` (production only, not needed in dev docker testing)

4. Body tab:
   - Select "raw" and "JSON"
   - Body content:
     ```json
     {
       "priceId": "price_1234567890"
     }
     ```

5. Click "Send" to execute the request

### Hopscotch

1. Create a new request with method: POST
2. URL: 
   - Local: `http://localhost:54321/functions/v1/create-subscription`
   - Production: `https://YOUR_SUPABASE_URL/functions/v1/create-subscription`

3. Headers section:
   - Add header: Authorization = `Bearer USER_JWT_TOKEN` from the end users supabase login
   - Add header: Content-Type = `application/json`
   - Add header: apikey = `YOUR_ANON_KEY` (production only, not needed in dev docker testing)

4. Body section:
   - Content type: `application/json`
   - Raw input:
     ```json
     {
       "priceId": "price_1234567890"
     }
     ```

5. Click "Send" to execute the request


## Implementation Details

This function:
1. Authenticates the user using their JWT token received during their signup/signin supabase auth
2. Ensures the user has a Stripe customer ID (creates one if needed)
3. Creates an incomplete subscription with Stripe
4. Returns the subscription ID and client secret for payment completion

The client secret should be used with Stripe's Payment Element to complete the subscription setup on the frontend.

## Troubleshooting

### Import Errors

If you encounter errors like:
```
worker boot error: failed to create the graph: Relative import path "@supabase/supabase-js" not prefixed with / or ./ or ../
```

This is because Supabase Edge Functions use Deno, which requires either:
1. Full URLs for imports (recommended)
2. Import maps properly configured

The function uses direct ESM URLs for dependencies:
```typescript
import Stripe from "https://esm.sh/stripe@14.21.0"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
```

This approach ensures compatibility across all environments (local development and production).