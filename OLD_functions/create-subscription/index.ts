// functions/create-subscription/index.ts

import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import Strip<PERSON> from "https://esm.sh/stripe@14.21.0"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { createCorsResponse, createErrorResponse } from "../_shared/cors.ts"

// ─── Env vars ─────────────────────────────────────────────────────────────
const SP_SUPABASE_URL              = Deno.env.get("SP_SUPABASE_URL")!
const SP_SUPABASE_ANON_KEY         = Deno.env.get("SP_SUPABASE_ANON_KEY")!
const SP_SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SP_SUPABASE_SERVICE_ROLE_KEY")!
const SP_STRIPE_SECRET_KEY         = Deno.env.get("SP_STRIPE_SECRET_KEY")!

// ─── Clients ──────────────────────────────────────────────────────────────
const supabaseAuth  = createClient(SP_SUPABASE_URL, SP_SUPABASE_ANON_KEY)
const supabaseAdmin = createClient(SP_SUPABASE_URL, SP_SUPABASE_SERVICE_ROLE_KEY)
const stripe        = new Stripe(SP_STRIPE_SECRET_KEY, { apiVersion: "2022-11-15" })

// ─── Auth helper ──────────────────────────────────────────────────────────
async function authenticate(req: Request) {
  const auth = req.headers.get("Authorization")?.split(" ")[1]
  if (!auth) return { user: null, error: "Missing Authorization header" }

  const { data: { user }, error } = await supabaseAuth.auth.getUser(auth)
  return { user, error: error?.message }
}

// ─── Edge Function Entrypoint using Deno.serve ─────────────────────────────
Deno.serve(async (req) => {
  // CORS preflight
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin":  "*",
        "Access-Control-Allow-Headers": "Authorization, apikey, Content-Type, x-client-info",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      },
    })
  }

  try {
    // 1) Parse input
    const { priceId } = await req.json()
    if (!priceId) {
      return createErrorResponse("Price ID is required", 400)
    }

    // 2) Authenticate user
    const { user, error: authError } = await authenticate(req)
    if (authError || !user) {
      return createErrorResponse(authError || "Authentication failed", 401)
    }

    // 3) Retrieve or create Stripe customer
    const { data: profile, error: profileError } = await supabaseAdmin
      .from("profiles")
      .select("stripe_customer_id,email")
      .eq("id", user.id)
      .single()
    if (profileError) throw profileError

    let customerId = profile.stripe_customer_id
    if (!customerId) {
      const cust = await stripe.customers.create({ email: profile.email! })
      customerId = cust.id
      await supabaseAdmin
        .from("profiles")
        .update({ stripe_customer_id: customerId })
        .eq("id", user.id)
    }

    // Prevent multiple active subscriptions for the same product
    const existingList = await stripe.subscriptions.list({
      customer: customerId,
      status: "active",
      expand: ["data.items"]
    })
    if (existingList.data.some((sub: Stripe.Subscription) =>
      sub.items.data.some((item: Stripe.SubscriptionItem) => item.price.id === priceId)
    )) {
      return createErrorResponse(
        "You already have an active subscription for this product.",
        400
      )
    }

    // 4) Create subscription
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: "default_incomplete",
      expand: ["latest_invoice.payment_intent"],
    })

    const intent = subscription.latest_invoice?.payment_intent
    if (!intent || typeof intent === "string") {
      return createErrorResponse("Failed to create payment intent", 500)
    }

    // 5) Return the client secret
    return createCorsResponse({
      subscriptionId: subscription.id,
      clientSecret: intent.client_secret,
    })

  } catch (err) {
    console.error("create-subscription error:", err)
    return createErrorResponse("Internal server error", 500)
  }
})
