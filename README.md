# Notes

## TODO

- Make sure a user cannot signup for multiple subscriptions - check if they already have a pending subscription in stripe or an existing subscription

## For CLI setup for edge functions

1. https://supabase.com/docs/guides/local-development
   1. Create new project folder
   2. Inside it run `npm install supabase --save-dev`
   3. Then run `npx supabase init`
   4. Do next steps (like [docker setup](https://docs.docker.com/desktop/) and npx supabase start)
2. Make sure deno is installed on machine
   1. https://deno.land/manual/getting_started/setup_your_environment
   2. https://docs.deno.com/runtime/
3. https://supabase.com/docs/guides/functions/local-quickstart
   1. Create edge function like `supabase functions new hello-world`


## Testing

Should use cli secrets feature for functions. Make sure they don't start with SUPABASE, this would conflict with a built in security for env filtering. Environment variables whose names start with “SUPABASE_” are treated as internal system variables by the Supabase Edge Functions runtime and are intentionally skipped when you serve locally. This is a security measure to prevent leaking your project’s anon and service-role keys (and other Supabase internals) into function code.

```bash
npx supabase secrets set SUP_SERVICE_ROLE_KEY=your_service_role_key
npx supabase secrets set ANON_KEY=your_anon_key
```

`npx supabase status` to see if docker is up and running
stop and start too 

deno test --allow-net --allow-env=LOCAL_SUPABASE_URL,LOCAL_SUPABASE_ANON_KEY list-subscriptions-integration-test.ts

### example deploy function to local docker

`npx supabase functions serve create-subscriptions`

# all functions with env

`npx supabase functions serve --env-file .env`

## Deploy edge functions to production

https://supabase.com/docs/guides/functions/deploy

## Edge Function tips

https://supabase.com/docs/guides/functions/development-tips

### Organizing your Edge Functions#

We recommend developing "fat functions". This means that you should develop few large functions, rather than many small functions. One common pattern when developing Functions is that you need to share code between two or more Functions. To do this, you can store any shared code in a folder prefixed with an underscore (_). We also recommend a separate folder for Unit Tests including the name of the function followed by a -test suffix.

We recommend this folder structure:

└── supabase
    ├── functions
    │   ├── import_map.json # A top-level import map to use across functions.
    │   ├── _shared
    │   │   ├── supabaseAdmin.ts # Supabase client with SERVICE_ROLE key.
    │   │   └── supabaseClient.ts # Supabase client with ANON key.
    │   │   └── cors.ts # Reusable CORS headers.
    │   ├── function-one # Use hyphens to name functions.
    │   │   └── index.ts
    │   └── function-two
    │   │   └── index.ts
    │   └── tests
    │       └── function-one-test.ts
    │       └── function-two-test.ts
    ├── migrations
    └── config.toml




### Email and OTP

When you run `supabase start` locally, the auth service doesn’t actually send real emails—instead it captures them in an “Inbucket” web UI so you can inspect them. In your terminal right after `supabase start` you should see something like:

```
API URL:        http://127.0.0.1:54321
…
Studio URL:     http://127.0.0.1:54323
Inbucket URL:   http://127.0.0.1:54324
…
```

To grab your OTP:

1. Open your browser to **[http://localhost:54324](http://localhost:54324)** ([webscope.io][1])
2. Look under “Monitor” or “Recent Mailboxes” for the email address you just used to sign up
3. Click the message and copy the 6-digit code from the email body
4. Back in your app, call `supabase.auth.verifyOtp({ email, token: '<that-code>', type: 'email' })`

If for some reason you don’t see it in Inbucket, you can also tail the auth container’s logs:

```bash
docker compose logs -f auth
```

(gotrue will log the OTP in info-level messages)

[1]: https://www.webscope.io/blog/supabase-email-workflow "Creating an email workflow for local Supabase development"
